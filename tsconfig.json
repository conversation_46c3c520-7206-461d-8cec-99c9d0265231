{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@assets/*": ["./src/assets/*"],
      "@components/*": ["./src/components/*"],
      "@mocks/*": ["./src/mocks/*"],
      "@utils/*": ["./src/utils/*"],
      "@contexts/*": ["./src/contexts/*"],
      "@reducers/*": ["./src/reducers/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@pages/*": ["./src/pages/*"],
      "@services/*": ["./src/services/*"],
      "@src/*": ["./src/*"],
      "@styles/*": ["./src/styles/*"],
      "@typings/*": ["./src/typings/*"],
    },
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react",
    "typeRoots": ["node_modules/@types", "src/types"]
  },
  "include": ["src"]
}
