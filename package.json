{"name": "blip-insights-mfe", "version": "26.8.2", "private": true, "sideEffects": ["blip-ds"], "dependencies": {"@sentry/react": "^8.38.0", "@sentry/types": "^8.38.0", "blip-ds": "file:///lucas/stilingue/git/projects/blip-ds/blip-ds-0.0.1-development/package", "blip-services": "^1.9.0", "blip-tokens": "^1.53.0", "chart.js": "^4.4.8", "feature-toggle-client": "^2.0.0", "react-chartjs-2": "^5.3.0", "react-localization": "^1.0.19", "react-router-dom": "6.16.0"}, "scripts": {"start": "webpack serve --mode development --env development --port 5025 --hot", "validate": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx src", "build": "webpack --mode development", "build:production": "webpack --mode production --env production", "prepare": "husky install", "test": "jest --passWithNoTests --silent --noStackTrace --runInBand", "test:verbose": "jest --passWithNoTests --runInBand", "test:unit": "npm test -- --watch -c jest-unit-config.js", "test:integration": "npm test -- --watch -c jest-integration-config.js", "test:coverage": "npm test -- --coverage"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.14.7", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.14.5", "@babel/runtime": "^7.13.10", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^5.1.3", "@types/fork-ts-checker-webpack-plugin": "^0.4.5", "@types/jest": "^29.5.11", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@types/react-router-dom": "^5.3.3", "@types/webpack": "^4.41.30", "@types/webpack-dev-server": "^3.11.5", "@typescript-eslint/eslint-plugin": "^4.28.3", "@typescript-eslint/parser": "^4.28.3", "babel-jest": "^26.6.3", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.8.1", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^6.2.12", "html-to-react": "^1.4.5", "husky": "^5.2.0", "ignore-loader": "^0.1.2", "jest": "^26.6.0", "jest-junit": "^16.0.0", "jest-svg-transformer": "^1.0.0", "prettier": "^2.3.2", "prop-types": "^15.7.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.58.0", "sass-loader": "^13.2.0", "style-loader": "^3.3.3", "to-string-loader": "^1.2.0", "ts-node": "^10.8.1", "tsconfig-paths-webpack-plugin": "^3.5.1", "typescript": "^4.3.5", "webpack": "^5.44.0", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2"}, "__mocks__": {"blip-services": "<rootDir>/__mocks__/blip-services.js"}}