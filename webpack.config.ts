import webpack from 'webpack';
import path from 'path';
import ForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import CopyWebpackPlugin from 'copy-webpack-plugin';


const releaseVersion = process.env.RELEASE_VERSION;

// 2. IMPORTANT: Provide a fallback for local development.
//    When you run `npm start` locally, `RELEASE_VERSION` won't be set.
//    This ensures your app doesn't crash and has a meaningful default.
const appVersion = releaseVersion || `local-dev-${new Date().getTime()}`;

// 3. Add a log for sanity-checking in your CI pipeline.
//    This helps you immediately see what version the build is using.
console.log(`-- Building MFE with Release Version: ${appVersion} --`);


module.exports = (env: { production: boolean }) => {
  return {
    mode: env.production ? 'production' : 'development',
    devtool: env.production ? 'hidden-source-map' : 'source-map',
    target: 'web',
    output: {
      publicPath: '/dist',
      clean: true,
      sourceMapFilename: '[file].map',
    },
    resolve: {
      extensions: ['.ts', '.tsx', '.js'],
      plugins: [new TsconfigPathsPlugin()],
      alias: {
        '~': path.resolve(__dirname, 'node_modules'),
      },
    },
    module: {
      rules: [
        {
          test: /\.(ts|js)x?$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
            },
          },
        },
        {
          test: /\.css$/,
          use: ['css-loader'],
          sideEffects: true,
        },
        {
          test: /\.(jpe?g|gif|png|svg)$/i,
          type: 'asset/inline',
          sideEffects: true,
        },
        {
          test: /\.scss$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'sass-loader',
              options: {
                sassOptions: {
                  includePaths: [path.resolve(__dirname, 'node_modules')],
                },
              },
            },
          ],
          sideEffects: true,
        },
        // Ignore source map files to prevent webpack from trying to parse them as modules
        {
          test: /\.map$/,
          use: 'ignore-loader',
        },
      ],
    },
    plugins: [
      new ForkTsCheckerWebpackPlugin({
        async: false,
        eslint: {
          files: './src/**/*.{ts,tsx,js,jsx}',
        },
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, './conf/settings.json'),
            to: path.resolve(__dirname, './dist/settings.json'),
          },
        ],
      }),
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 1,
      }),
      new webpack.DefinePlugin({
        'process.env.RELEASE_VERSION': JSON.stringify(appVersion || 'unknown'),
      }),
    ],
    devServer: {
      allowedHosts: ['*'],
      disableHostCheck: true,
      historyApiFallback: true,
      hot: true,
      serveIndex: false,
      contentBase: path.join(__dirname, 'src'),
      open: true,
      compress: true,
      watchContentBase: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
        'Access-Control-Allow-Headers': '*',
      },
    },
  };
};
