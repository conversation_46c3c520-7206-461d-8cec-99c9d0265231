export class BlipService {
  constructor(config) {
    this.config = config;
    this.connected = false;
  }

  async connect() {
    this.connected = true;
    return Promise.resolve();
  }

  async disconnect() {
    this.connected = false;
    return Promise.resolve();
  }

  async processCommand(command, timeout) {
    return Promise.resolve({
      id: command.id,
      method: command.method,
      status: 'success',
      resource: { items: [] },
    });
  }

  isConnected() {
    return this.connected;
  }
}
