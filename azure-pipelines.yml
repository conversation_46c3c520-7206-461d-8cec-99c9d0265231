trigger:
  - master

pool:
  vmImage: ubuntu-latest

variables:
  sonarQubeTags: blip
  sonarQubeKey: blip-insights-mfe
  sonarQubeName: Blip Insights MFE
  framework: javascript
  BuildCounter: $[counter('buildCounter',1)]

resources:
  repositories:
    - repository: templates
      type: git
      name: Operações/template-take-blip
      ref: refs/tags/v1.4.69

extends:
  template: template-pipeline.yml@templates
  parameters:
    nodeVersion: 16.x
    packagename: blip-insights-mfe
    type: javascript
    to: artifact
    createZipPackage: false
    npmCmds:
      - npm install
      - npm run test
      - npm run build:production
