module.exports = {
  roots: ['<rootDir>/src'],
  collectCoverage: true,
  collectCoverageFrom: ['**/*.{ts,tsx}', '!src/constants.ts', '!src/typings/*'],
  coveragePathIgnorePatterns: ['<rootDir>/src/types'],
  // setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'], // Add this line to mock WebSocket connection and blip-services
  transform: {
    '\\.[jt]sx?$': 'babel-jest',
    '^.+\\.svg$': 'jest-svg-transformer',
  },
  transformIgnorePatterns: ['<rootDir>/node_modules/(?!blip-ds)'],
  moduleNameMapper: {
    '\\.(jpg|ico|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
    '\\.(css|less|scss)$': '<rootDir>/__mocks__/fileMock.js',
    '@contexts/(.*)': '<rootDir>/src/contexts/$1',
    '@reducers/(.*)': '<rootDir>/src/reducers/$1',
    '@components/(.*)': '<rootDir>/src/components/$1',
    '@mocks/(.*)': '<rootDir>/src/mocks/$1',
    '@utils/(.*)': '<rootDir>/src/utils/$1',
    '@pages/(.*)': '<rootDir>/src/pages/$1',
    '@hooks/(.*)': '<rootDir>/src/hooks/$1',
    '@services/(.*)': '<rootDir>/src/services/$1',
    '@src/(.*)': '<rootDir>/src/$1',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
};
