{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"indent": ["error", 2], "quotes": ["error", "single", {"avoidEscape": true}], "jsx-quotes": ["error", "prefer-double"], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "react/prop-types": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-empty-function": "off"}, "settings": {"react": {"pragma": "React", "version": "detect"}}}