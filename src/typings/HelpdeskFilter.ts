export interface HelpdeskFilter {
  channels: string[];
  routerIds: string[];
  botIds: string[];
  queues: string[];
  startDate: Date;
  endDate: Date;
}

export interface DataFrontData {
  data: string[];
  tickets_abertos: number[];
  tickets_fechados: number[];
  tempo_medio_atendimento: string[];
  tempo_medio_espera: string[];
  tempo_medio_resposta_atendente: string[];
  tempo_medio_fila: string[];
  tempo_medio_primeira_resposta_atendente: string[];
}