export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface BaseFilter {
  query_name: string;
  parameters?: {
    tenant_id?: string;
    previous_start_date?: string;
    previous_end_date?: string;
    start_date?: string;
    end_date?: string;
  };
  filters: {
    data?: DateRange;
    Date?: DateRange;
    data_fechamento?: DateRange;
    data_criacao?: DateRange;
    Canal?: string[];
    RouterBotId?: string[];
    RouterId?: string[];
    BotId?: string[];
    Category?: string[];
    Template?: string[];
    Campaign?: string[];
  }
}
