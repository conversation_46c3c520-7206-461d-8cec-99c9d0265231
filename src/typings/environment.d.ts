// src/environment.d.ts

// By declaring this in a .d.ts file, we inform TypeScript about
// the custom environment variable we are injecting via Webpack.
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      /**
       * The Sentry release version string injected by Webpack at build time.
       */
      RELEASE_VERSION: string;
    }
  }
}

// This empty export statement turns the file into a module,
// which is required for `declare global` to work correctly.
export {};
