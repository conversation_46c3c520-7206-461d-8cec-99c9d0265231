import { InsightsSegmentationData } from '@services/BlipInsightsService';

export interface OverviewFilter {
  channels: string[];
  routerIds: string[];
  botIds: string[];
  startDate: Date;
  endDate: Date;
}

export type InsightBaseData = string[][];

export interface BaseGraphData {
  date_series: string[];
  data: UserGraphData | MessagesGraphData;
}

export type MessagesGraphType = {
  date_series: string[];
  data: MessagesGraphData;
};

export type UserGraphType = {
  date_series: string[];
  data: UserGraphData;
};

export interface MessagesGraphData {
  sentMessages: number[];
  receivedMessages: number[];
  activeMessages: number[];
  perUserMessage: number[];
}

export interface UserGraphData {
  dailyActiveUsers: number[];
  engagedUsersData: number[];
  recurrencyRateData: number[];
  engagementRateData: number[];
}
export interface InsightsRequestData {
  dados_front_segmentacao: string[];
  dados_front: BaseGraphData;
}

export interface MessagesInsightFrontData {
  data: string[];
  sentMessages: number[];
  receivedMessages: number[];
  activeMessages: number[];
  perUserMessage: number[];
}

export interface UsersInsightFrontData {
  data: string[];
  dailyActiveUsers: number[];
  engagedUsersData: number[];
  recurrencyRateData: number[];
  engagementRateData: number[];
}

export interface DatabricksInsightsServiceRequestData {
  inputs: {
    dados_front_segmentacao: InsightsSegmentationData;
    dados_front: MessagesInsightFrontData | UsersInsightFrontData;
    tipo_analise: 'msgs' | 'users';
  };
}
