export interface BarData {
  value: number;
  tooltip: string;
}

export interface IndicatorData {
  status: string;
  variation: string;
  revertVariationChipColor: boolean;
  label?: string; // Optional text for the bar, used for labels
  type?: FunnelChartType; // Optional type for the indicator, used for labels
}

export interface ChartSectionData {
  bars: BarData;
  indicators: IndicatorData;
}

export interface FunnelChartData {
  sent: ChartSectionData;
  received: ChartSectionData;
  read: ChartSectionData;
  responded: ChartSectionData;
  conversion: ChartSectionData;
}

export type FunnelChartType = 'sent' | 'received' | 'read' | 'responded' | 'conversion' | 'default';
