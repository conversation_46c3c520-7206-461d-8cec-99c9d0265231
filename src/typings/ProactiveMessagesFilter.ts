export interface ProactiveMessagesFilter {
  routerIds: string[];
  botIds: string[];
  category: string[];
  template: string[];
  campaign: string[];
  startDate: Date;
  endDate: Date;
}

export type FunnelQueryType =
  | 'tooltips'
  | 'enviadas'
  | 'percentual-de-falhas'
  | 'percentual-comparativo-de-falhas'
  | 'recebidas'
  | 'percentual-de-recebimento'
  | 'percentual-comparativo-de-recebimento'
  | 'lidas'
  | 'percentual-de-leitura'
  | 'percentual-comparativo-de-leitura'
  | 'respondidas'
  | 'percentual-de-resposta'
  | 'percentual-comparativo-de-resposta'
  | 'conversão';
export interface HorizontalBarChartData {
  label: string;
  value: number;
  percentage: number;
}

export interface PerformanceFrontData {
  data: string[];
  sentMessages: number[];
  receivedMessages: number[];
  answeredMessages: number[];
  engagementRate: number[];
}

export type PerformanceChartType = {
  date_series: string[];
  data: PerformanceChartData;
};
export interface PerformanceChartData {
  messagesSent: number[];
  messagesReceived: number[];
  messagesResponded: number[];
  responsesCount: number[];
}