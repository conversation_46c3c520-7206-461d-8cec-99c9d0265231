/* eslint-disable @typescript-eslint/no-empty-function */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { App } from '../App';

global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
};
// Mock the BlipService context
jest.mock('@contexts/blip-service-context', () => ({
  BlipServiceProvider: ({ children }: { children: React.ReactNode }) => children,
  useBlipService: () => ({
    msgingInstance: null,
    connected: false,
    isConnecting: false,
    connect: jest.fn(),
    connectionError: null,
  }),
}));

// Mock the BlipServiceSingleton
jest.mock('@contexts/blip-service-context/BlipServiceSingleton', () => ({
  connect: jest.fn().mockResolvedValue({}),
}));

jest.mock('@services/QueryFetchers', () => ({
  fetchChannels: jest.fn(),
  fetchRouters: jest.fn(),
  fetchBots: jest.fn(),
  fetchQueues: jest.fn(),
}));

describe('App', () => {
  test('renders App component', () => {
    render(
      <App
        language="pt"
        authtoken="teste"
        currenttenantid="tenant"
        settings="https://hmgportalmfe.blip.ai/settings.json"
        user={{
          fullName: 'Name Full',
          alternativeAccount: '<EMAIL>',
          identity: '<EMAIL>',
          email: '<EMAIL>',
          phoneNumber: '+55 55 55555 5555',
          photoUri: 'https://lh3.googleusercontent.com/a/sdfsdf=s96-c',
          timeZoneName: 'E. South America Standard Time',
          culture: 'pt-BR',
          extras: {
            lastUsedTenants: '[]',
            isOldUser: 'true',
            cookies: '{}',
            initialSetupDone: 'true',
          },
          creationDate: '2023-05-19T14:20:13.340Z',
        }}
      />
    );

    screen.debug();
  });
});
