import featureToggleService, { BLIP_INSIGHTS_FEATURE_TOGGLES, Configuration } from '@services/FeatureToggleService';
import { User } from '@typings/User';
import { SentryClient, SentryConfig } from '@utils/monitoring/SentryClient';
import React, { createContext, useState, useEffect } from 'react';
import { constants } from '@src/constants';

type AppContextData = {
  tenantId: string;
  settings: Configuration;
  showToast?: boolean;
  setShowToast?: (showToast: boolean) => void;
  user: User;
  authtoken: string | undefined;
  isCheckingUserAcess: boolean;
  setIsCheckingUserAcess: (showToast: boolean) => void;
  userHasAccess: boolean;
  setUserHasAccess: (showToast: boolean) => void;
  getFeatureToggleService: () => Promise<typeof featureToggleService>;
};

const AppContext = createContext<AppContextData>({} as AppContextData);

const AppProvider: React.FC<{
  tenantId: string;
  user: User;
  settings: string;
  authtoken?: string;
  children: React.ReactNode;
}> = ({ tenantId, user, authtoken, settings, children }) => {
  const [showToast, setShowToast] = React.useState<boolean>(false);
  const [isCheckingUserAcess, setIsCheckingUserAcess] = useState<boolean>(true);
  const [userHasAccess, setUserHasAccess] = useState<boolean>(false);
  const [startedFeatureToggleService, setStartedFeatureToggleService] = useState<boolean>(false);
  const [configurationSettings, setConfigurationSettings] = useState<Configuration | null>(null);

  // Ensure user.identity is set for BlipService connection
  // If not provided, use email as identity
  const userWithIdentity: User = {
    ...user,
    identity: user.identity || user.email,
  };

  const tenantIdFromUrl = window.location.host.split('.')[0];
  const tenantIdFromParameter = window.location.search.split('tenantId=')[1];

  if (tenantId && tenantIdFromUrl !== tenantId && !tenantIdFromParameter) {
    window.location.href = '/application';
  }

  tenantId = tenantIdFromParameter || tenantId || tenantIdFromUrl;

  const getFeatureToggleService = async (): Promise<typeof featureToggleService> => {
    if (!startedFeatureToggleService) {
      await startFeatureToggleService(user, settings);
      setStartedFeatureToggleService(true);
    }
    return featureToggleService;
  };

  const startSentry = async () => {
    try {
      const ftService = await getFeatureToggleService();
      const sentrySettings = await ftService.getFeatureToggleConfiguration(
        BLIP_INSIGHTS_FEATURE_TOGGLES.SENTRY_BLIP_INSIGHTS_CONFIG,
      );

      if (sentrySettings?.isEnabled) {
        SentryClient.startSentry(
          {
            dsn: constants.sentryDSN,
            sampleRate: sentrySettings.sampleRate,
            tracesSampleRate: sentrySettings.tracesSampleRate,
          } as SentryConfig,
          constants.blobPath
        );
      }
    } catch (error) {
      console.error('Error monitoring tool:', error);
    }
  };

  // Initialize feature toggle service on mount
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Load configuration first
        const config = await getFeatureToggleConfiguration(settings);
        setConfigurationSettings(config);

        // Initialize feature toggle service
        await getFeatureToggleService();

        // Start Sentry after feature toggle is ready
        await startSentry();
      } catch (error) {
        console.error('Error initializing services:', error);
      }
    };

    initializeServices();
  }, [settings, user]); // eslint-disable-line react-hooks/exhaustive-deps

  const context = {
    tenantId,
    settings: configurationSettings || ({} as Configuration),
    showToast,
    setShowToast,
    user: userWithIdentity,
    authtoken: authtoken,
    isCheckingUserAcess,
    setIsCheckingUserAcess,
    userHasAccess,
    setUserHasAccess,
    getFeatureToggleService,
  } as AppContextData;

  return <AppContext.Provider value={context}>{children}</AppContext.Provider>;
};

export { AppProvider, AppContext };

export function useAppContext(): AppContextData {
  const context = React.useContext(AppContext);

  if (!context) {
    throw new Error('use app context must be used within an AppProvider');
  }

  return context;
}

async function startFeatureToggleService(user: User, settings: string) {
  const config = await getFeatureToggleConfiguration(settings);
  await featureToggleService.initFeatureToggle(user, config);
  return featureToggleService;
}

async function getFeatureToggleConfiguration(settings: string): Promise<Configuration> {
  const data = await fetch(settings);
  const json: Configuration = await data.json();
  return {
    ldclientSdkKey: json.ldclientSdkKey || '',
    ldProxyBaseUrl: json.ldProxyBaseUrl || '',
    ldProxyEventsUrl: json.ldProxyEventsUrl || '',
    ldProxyClientStream: json.ldProxyClientStream || '',
    ldStreamReconnectDelay: json.ldStreamReconnectDelay,
    blipDomain: json.blipDomain || '',
    blipDomainUrl: json.blipDomainUrl || '',
    blipWebsocketHostName: json.blipWebsocketHostName || '',
    blipWebsocketHostNameTenant: json.blipWebsocketHostNameTenant || '',
    blipWebsocketPort: json.blipWebsocketPort || '',
    blipWebsocketScheme: json.blipWebsocketScheme || '',
  };
}
