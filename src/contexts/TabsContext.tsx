import React, { useState, createContext, useContext } from 'react';

type TabContextType = {
  activeTab: string;
  setActiveTab: (tab: string) => void;
};
const TabContext = createContext<TabContextType | undefined>(undefined);

export const TabProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [activeTab, setActiveTab] = useState('overview');

  return <TabContext.Provider value={{ activeTab, setActiveTab }}>{children}</TabContext.Provider>;
};
export const useTabContext = () => {
  const context = useContext(TabContext);
  if (!context) {
    throw new Error('useTabContext must be used within a TabProvider');
  }
  return context;
};

export const TAB_NAMES = {
  overview: 'overview',
  helpdesks: 'helpdesks',
  proactiveMessages: 'proactive-messages',
  proactiveMessagesTemplatesComparing: 'proactive-messages-templates-comparing',
  datamind: 'datamind',
};
