import React, { useState, createContext, useContext, useMemo } from 'react';

type TemplateContextType = {
  filterTemplates: { [value: string]: string | number | boolean; label: string }[];
  setFilterTemplates: (templates: { [value: string]: string | number | boolean; label: string }[]) => void;
  activeTemplateA: string;
  setActiveTemplateA: (template: string) => void;
  activeTemplateB: string;
  setActiveTemplateB: (template: string) => void;
};
const TemplateContext = createContext<TemplateContextType | undefined>(undefined);

export const TemplateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [filterTemplates, setFilterTemplates] = useState(
    {} as { [value: string]: string | number | boolean; label: string }[]
  );
  const [activeTemplateA, setActiveTemplateA] = useState<string>('');
  const [activeTemplateB, setActiveTemplateB] = useState<string>('');

  const context = useMemo(
    () => ({
      filterTemplates,
      setFilterTemplates,
      activeTemplateA,
      setActiveTemplateA,
      activeTemplateB,
      setActiveTemplateB,
    }),
    [filterTemplates, activeTemplateA, activeTemplateB]
  );

  return <TemplateContext.Provider value={context}>{children}</TemplateContext.Provider>;
};
export const useTemplateContext = () => {
  const context = useContext(TemplateContext);
  if (!context) {
    throw new Error('useTemplateContext must be used within a TemplateProvider');
  }
  return context;
};
