import React, { FC, createContext, useState, PropsWithChildren, useCallback, useMemo, useEffect } from 'react';
import { BlipService } from 'blip-services';
import { WebSocketParams } from '@src/typings/WebSocketParams';
import { useAppContext } from '../AppContext';
import BlipServiceSingleton from './BlipServiceSingleton';

export interface BlipServiceContextData {
  msgingInstance: BlipService | null;
}

export interface BlipServiceContextValue {
  webSocketParams: WebSocketParams;
  blipServiceConfig?: any;
  applicationName?: string;
}

export const BlipServiceContext = createContext<BlipServiceContextData>({} as BlipServiceContextData);

export const BlipServiceProvider: FC<PropsWithChildren<BlipServiceContextValue>> = ({
  children,
  webSocketParams,
  blipServiceConfig = {},
  applicationName,
}) => {
  const [msgingInstance, setMsgingInstance] = useState<BlipService | null>(null);
  const [connected, setConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const { user } = useAppContext();
  const tenantId = 'listen-and-reach'; // TODO replace with user.tenantId or similar

  const connect = useCallback(async () => {
    if (isConnecting || connected) {
      return;
    }
    setIsConnecting(true); // Set connecting state
    setConnected(false);

    // Validate required data
    if (!tenantId || !user?.identity || !user?.email || !blipServiceConfig.authToken) {
      const error = 'Missing required data for BlipService connection';
      console.error('[BlipService] ❌ Connection validation failed:', {
        error,
        availableData: {
          tenantId: !!tenantId,
          userIdentity: !!user?.identity,
          userEmail: !!user?.email,
          authToken: !!blipServiceConfig.authToken,
          user: user ? { ...user, identity: user.identity || 'NOT_SET' } : 'NOT_SET',
        },
      });
      return;
    }
    try {
      const serviceConfiguration = {
        tenant: tenantId,
        blipDomain: blipServiceConfig.blipDomain || 'blip.ai',
        blipWebsocketPort: blipServiceConfig.blipWebsocketPort || '443',
        blipWebsocketScheme: blipServiceConfig.blipWebsocketScheme || 'wss',
        blipWebsocketHostName: webSocketParams.blipWebsocketHostName,
        blipWebsocketHostNameTenant: webSocketParams.blipWebsocketHostNameTenant,
        applicationName,
        ...blipServiceConfig,
      };

      const service = await BlipServiceSingleton.connect(serviceConfiguration, user, blipServiceConfig.authToken);
      setMsgingInstance(service);
      setConnected(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error';
      console.error('[BlipService] ❌ Connection failed:', {
        error: errorMessage,
        fullError: error,
        tenantId,
        userIdentity: user?.identity,
        hasAuthToken: !!blipServiceConfig.authToken,
      });
      setConnected(false);
      setMsgingInstance(null);
    } finally {
      setIsConnecting(false); // Reset connecting state
    }
  }, [connected, isConnecting]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (isConnecting || connected) {
      return;
    }

    if (tenantId && webSocketParams && user.identity && user.email) {
      connect();
    }
  }, [tenantId, user, webSocketParams]); // eslint-disable-line react-hooks/exhaustive-deps

  return <BlipServiceContext.Provider value={{ msgingInstance }}>{children}</BlipServiceContext.Provider>;
};

export function useBlipService(): BlipServiceContextData {
  const context = React.useContext(BlipServiceContext);
  if (!context) {
    throw new Error('useBlipService must be used within a BlipServiceProvider');
  }
  return context;
}
