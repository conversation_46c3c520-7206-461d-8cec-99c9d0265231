import React, { useState, createContext, useContext, useMemo, useCallback } from 'react';
import { BlipService } from 'blip-services';
import { WebSocketParams } from '@src/typings/WebSocketParams';
import { useAppContext } from '../AppContext';
import BlipServiceSingleton from './BlipServiceSingleton';

export interface BlipServiceContextData {
  msgingInstance: BlipService | null;
  connected: boolean;
  isConnecting: boolean;
  connect: () => Promise<void>;
  connectionError: string | null;
}

export interface BlipServiceContextValue {
  webSocketParams: WebSocketParams;
  blipServiceConfig?: any;
  applicationName?: string;
}

export const BlipServiceContext = createContext<BlipServiceContextData>({
  msgingInstance: null,
  connected: false,
  isConnecting: false,
  connect: async () => {},
  connectionError: null,
});

export const BlipServiceProvider: React.FC<React.PropsWithChildren<BlipServiceContextValue>> = ({
  children,
  webSocketParams,
  blipServiceConfig = {},
  applicationName = 'blip-insights-mfe',
}) => {
  const [msgingInstance, setMsgingInstance] = useState<BlipService | null>(null);
  const [connected, setConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const { user, tenantId } = useAppContext();

  const connect = useCallback(async () => {
    if (connected && msgingInstance) {
      return;
    }

    if (isConnecting) {
      return;
    }

    if (!tenantId || !user?.identity || !user?.email || !blipServiceConfig.authToken) {
      const error = 'Missing required data for BlipService connection';
      console.error('[BlipService] Connection validation failed:', { error });
      throw new Error(error);
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const serviceConfiguration = {
        tenant: tenantId,
        blipDomain: blipServiceConfig.blipDomain || 'blip.ai',
        blipWebsocketPort: blipServiceConfig.blipWebsocketPort || '443',
        blipWebsocketScheme: blipServiceConfig.blipWebsocketScheme || 'wss',
        blipWebsocketHostName: webSocketParams.blipWebsocketHostName,
        blipWebsocketHostNameTenant: webSocketParams.blipWebsocketHostNameTenant,
        applicationName,
        ...blipServiceConfig,
      };

      const instance = await BlipServiceSingleton.connect(serviceConfiguration, user, blipServiceConfig.authToken);

      setMsgingInstance(instance);
      setConnected(true);
    } catch (error) {
      console.error('[BlipService] Connection failed:', error);
      setConnectionError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsConnecting(false);
    }
  }, [connected, isConnecting, msgingInstance, webSocketParams, blipServiceConfig, applicationName, user, tenantId]);

  const context = useMemo(
    () => ({
      msgingInstance,
      connected,
      isConnecting,
      connect,
      connectionError,
    }),
    [msgingInstance, connected, isConnecting, connect, connectionError]
  );
  return <BlipServiceContext.Provider value={context}>{children}</BlipServiceContext.Provider>;
};
export const useBlipService = () => {
  const context = useContext(BlipServiceContext);
  if (!context) {
    throw new Error('useBlipService must be used within a BlipServiceProvider');
  }
  return context;
};
