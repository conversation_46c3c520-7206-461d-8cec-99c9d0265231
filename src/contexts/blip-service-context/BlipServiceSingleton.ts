import { BlipService } from 'blip-services';
import { BlipServiceConfiguration } from 'blip-services/dist/blipService';
import { User } from '@src/typings/User';
import { createPromiseContainer, PromiseContainer } from '@src/utils/promise';

class BlipServiceSingleton {
  private connectionPromise: PromiseContainer<BlipService> = {} as PromiseContainer<BlipService>;

  async connect(blipServiceConf: BlipServiceConfiguration, user: User, authToken: string): Promise<BlipService> {
    if (this.connectionPromise.promise !== undefined) {
      await this.connectionPromise.promise;

      return this.connectionPromise.result;
    }

    const isBlipLoginEnabled = () => {
      try {
        const oidcId = localStorage.getItem('oidc.azureB2CData') ?? '';
        const oidcData = JSON.parse(oidcId);
        return oidcData.isEnabled;
      } catch {
        return false;
      }
    };

    const blipAccountIssuer = isBlipLoginEnabled() ? 'azure.blip.ai' : 'account.blip.ai';
    this.connectionPromise = createPromiseContainer();

    const service = new BlipService({
      ...blipServiceConf,
      blipAccountIssuer,
      routingRule: 'instance',
      blipDomainUrl: blipServiceConf.blipDomainUrl,
    });

    await service.connect(user.identity || '', authToken, `{email: ${user.email}}`);
    this.connectionPromise.result = service;
    this.connectionPromise.resolve();

    return this.connectionPromise.result;
  }
}

const blipServiceSingleton = new BlipServiceSingleton();

export default blipServiceSingleton;
