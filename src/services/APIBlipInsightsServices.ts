import { BaseFilter } from '@src/typings/BaseFilter';
import BlipInsightsService, { BackendResponse, InsightsResponse } from './BlipInsightsService';

export class APIBlipInsightsService extends BlipInsightsService {
  protected backendUrlStatements = 'https://api-firehose.blip.ai/api/blip-data-insights/v0/databricks/sql-warehouse';
  protected readonly backendUrlStatementsHmg = 'https://hmg-firehose.blip.tools/api/blip-data-insights/v0/databricks/sql-warehouse';
  protected readonly maxRetryCount = 3;
  protected DEFAULT_STATEMENTS_API_PATH = '';
  protected DEFAULT_INSIGHTS_API_PATH_HMG = '';

  protected issuer: string;

  constructor(authorization: string, settings: any) {
    super(authorization, settings);
    const url = window.location.href;
    if (url.includes('hmg.blip.ai') || url.includes('localhost')) {
      this.backendUrlStatements = this.backendUrlStatementsHmg;
      this.backendUrlInsights = this.backendUrlInsightsHmg;
    }
    this.authorization = authorization;
    this.issuer = this.BLIP_AZURE_ISSUER; // set default issuer
    this.setIssuer();
  }

  async getInsightsData(
    data: BaseFilter,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH
  ): Promise<InsightsResponse> {
    const response: Response = await this.executeRequest(data, endpoint, this.backendUrlStatements);
    return await response.json();
  }

  async getData(
    data: BaseFilter,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH,
    retryCount = 0
  ): Promise<BackendResponse> {
    const response: Response = await this.executeRequest(data, endpoint, this.backendUrlStatements);
    const responseData: BackendResponse = await response.json();
    return responseData as BackendResponse;
  }

  protected handlePendingRequest(
    data: BaseFilter,
    responseData: any,
    endpoint = '/',
    retryCount: number
  ): Promise<any> {
    console.warn('pending status returned for blip insights, retrying request...');
    if (retryCount >= this.maxRetryCount) {
      throw new Error('Max retry count reached for blip insights data');
    }

    retryCount++;

    return new Promise((resolve) => {
      setTimeout(async () => resolve(await this.getData(data, endpoint, retryCount)), 1000);
    });
  }

  protected async executeRequest(
    data: BaseFilter,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH,
    backendUrl = this.backendUrlStatements
  ): Promise<Response> {
    const url = `${backendUrl}${endpoint}`;

    return await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + this.authorization,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': '*',
        'Content-Type': 'application/json',
        'Http-Session-Authentication-External-Issuer': this.issuer,
      },
      body: JSON.stringify(data),
      credentials: 'include'
    });
  }
}
