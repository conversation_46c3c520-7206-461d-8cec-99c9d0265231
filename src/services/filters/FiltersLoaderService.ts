import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { OverviewFilter } from '@typings/OverviewFilter';
import { toBaseFilter } from '@utils/filters';

export class FiltersLoaderService {
  async getData(queryName: string, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilter(queryName, {} as OverviewFilter, tenantId));
  }

  async getAllOverviewFiltersData(tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse[]> {
    return Promise.all([
      this.getData('overview_channels', tenantId, blipInsightsService),
      this.getData('overview_routers', tenantId, blipInsightsService),
      this.getData('overview_bots', tenantId, blipInsightsService),
    ]);
  }

  async getAllHelpdesksFiltersData(tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse[]> {
    return Promise.all([
      this.getData('helpdesk_channels', tenantId, blipInsightsService),
      this.getData('helpdesk_routers', tenantId, blipInsightsService),
      this.getData('helpdesk_bots', tenantId, blipInsightsService),
      this.getData('helpdesk_queues', tenantId, blipInsightsService),
    ]);
  }

  async getAllProactiveMessagesFiltersData(tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse[]> {
    return Promise.all([
      this.getData('proactive_messages_routers', tenantId, blipInsightsService),
      this.getData('proactive_messages_bots', tenantId, blipInsightsService),
      this.getData('proactive_messages_category', tenantId, blipInsightsService),
      this.getData('proactive_messages_template', tenantId, blipInsightsService),
      this.getData('proactive_messages_campaign', tenantId, blipInsightsService),
    ]);
  }

  async getAllProactiveMessagesFiltersDataForTemplatesComparing(tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse[]> {
    return Promise.all([
      this.getData('proactive_messages_routers', tenantId, blipInsightsService),
      this.getData('proactive_messages_bots', tenantId, blipInsightsService),
    ]);
  }
}