import { DatabricksInsightsServiceRequestData, InsightsSegmentationData } from '@services/BlipInsightsService';
import { DataFrontData, HelpdeskFilter } from '@typings/HelpdeskFilter';
import { formatDateForQuery } from '@utils/time';

const CHANNEL_COLUMN_NAME = 'Canal';
const ROUTER_ID_COLUMN_NAME = 'RouterId';
const BOT_ID_COLUMN_NAME = 'BotId';
const QUEUE_COLUMN_NAME = 'Fila';

export const getBigNumbersPreviousPeriodQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  const millisecondsInADay = 1000 * 60 * 60 * 24;
  const millisDifference = helpdeskFilter.endDate.getTime() - helpdeskFilter.startDate.getTime();
  // assure that currentPeriodInDays is at least 1 day
  const currentPeriodInDays = millisDifference ? (millisDifference) / (millisecondsInADay) : 1;
  const selectedFilterCopy = Object.assign({}, helpdeskFilter);
  selectedFilterCopy.endDate = helpdeskFilter.startDate;
  const startDate = new Date(helpdeskFilter.startDate.getTime() - currentPeriodInDays * millisecondsInADay);
  selectedFilterCopy.startDate = startDate;
  return getBigNumbersQuery(tenantId, selectedFilterCopy);
};

export const getClosingTicketsHeatMapQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `${closingTicketsHeatMap} AND TenantId = "${tenantId}"`;
  query = addAllConditionsForFilter(query, helpdeskFilter, 'data_fechamento');
  query = `${query} GROUP BY dia_semanafechamento
  ORDER BY
  CASE dia_semanafechamento
    WHEN 'domingo' THEN 1
    WHEN 'segunda' THEN 2
    WHEN 'terça' THEN 3
    WHEN 'quarta' THEN 4
    WHEN 'quinta' THEN 5
    WHEN 'sexta' THEN 6
    WHEN 'sábado' THEN 7
  END`;
  return query;
};

export const getOpeningTicketsHeatMapQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `${openingTicketsHeatMap} AND TenantId = "${tenantId}"`;
  query = addAllConditionsForFilter(query, helpdeskFilter, 'data_criacao');
  query = `${query} GROUP BY dia_semanacriacao
  ORDER BY
  CASE dia_semanacriacao
    WHEN 'domingo' THEN 1
    WHEN 'segunda' THEN 2
    WHEN 'terça' THEN 3
    WHEN 'quarta' THEN 4
    WHEN 'quinta' THEN 5
    WHEN 'sexta' THEN 6
    WHEN 'sábado' THEN 7
  END`;
  return query;
};

export const getInsightsDataQuery = (tenantId: string, endDate: Date) => {
  const endDateFormatted = formatDateForQuery(endDate);
  return `
    SELECT
    data,
    SUM(tickets_abertos) AS abertos,
    SUM(tickets_fechados) AS fechados,
    CASE
      WHEN
        SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) >= 86400
      THEN
        CONCAT(
          CAST(
            FLOOR(SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) / 86400) AS STRING
          ),
          'd ',
          LPAD(
            CAST(
              FLOOR(
                MOD(SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0), 86400) / 3600
              ) AS STRING
            ),
            2,
            '0'
          ),
          ':',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_atendimento), 3600) / 60) AS STRING), 2, '0')
        )
      ELSE
        date_format(
          timestamp_seconds(
            CAST(SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS BIGINT)
          ),
          'HH:mm:ss'
        )
    END AS TMA,
    CASE
      WHEN
        SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) >= 86400
      THEN
        CONCAT(
          CAST(FLOOR(SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) / 86400) AS STRING),
          'd ',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_espera), 86400) / 3600) AS STRING), 2, '0'),
          ':',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_espera), 3600) / 60) AS STRING), 2, '0')
        )
      ELSE
        date_format(
          timestamp_seconds(CAST(SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS BIGINT)),
          'HH:mm:ss'
        )
    END AS TME,
    CASE
      WHEN
        SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) >= 86400
      THEN
        CONCAT(
          CAST(FLOOR(SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) / 86400) AS STRING),
          'd ',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_resposta), 86400) / 3600) AS STRING), 2, '0'),
          ':',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_resposta), 3600) / 60) AS STRING), 2, '0')
        )
      ELSE
        date_format(
          timestamp_seconds(CAST(SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS BIGINT)),
          'HH:mm:ss'
        )
    END AS TMR,
    CASE
      WHEN
        SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) >= 86400
      THEN
        CONCAT(
          CAST(FLOOR(SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) / 86400) AS STRING),
          'd ',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_fila), 86400) / 3600) AS STRING), 2, '0'),
          ':',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_fila), 3600) / 60) AS STRING), 2, '0')
        )
      ELSE
        date_format(
          timestamp_seconds(CAST(SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) AS BIGINT)),
          'HH:mm:ss'
        )
    END AS TMF,
    CASE
      WHEN
        SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) >= 86400
      THEN
        CONCAT(
          CAST(
            FLOOR(
              SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) / 86400
            ) AS STRING
          ),
          'd ',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_primeira_respota), 86400) / 3600) AS STRING), 2, '0'),
          ':',
          LPAD(CAST(FLOOR(MOD(SUM(tempo_primeira_respota), 3600) / 60) AS STRING), 2, '0')
        )
      ELSE
        date_format(
          timestamp_seconds(
            CAST(SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS BIGINT)
          ),
          'HH:mm:ss'
        )
    END AS TM1R
  FROM
    clients_trustedzone.ilab.gold_smb_desk_main_data
  WHERE
    TenantId = '${tenantId}'
    AND Data BETWEEN
      CASE
        WHEN
          date_format('${endDateFormatted}', 'yyyy-MM') >= date_format(current_date(), 'yyyy-MM')
        THEN
          trunc(add_months(current_date(), -1), 'MM')
        ELSE trunc('${endDateFormatted}', 'MM')
      END
    AND
      CASE
        WHEN
          date_format('${endDateFormatted}', 'yyyy-MM') >= date_format(current_date(), 'yyyy-MM')
        THEN
          last_day(add_months(current_date(), -1))
        ELSE last_day('${endDateFormatted}')
      END
  GROUP BY
    data`;
};

export const mapInsightsToDatabricksInsigthsRequestData = (
  dataArray: string[][],
  tenantId: string,
  filter: HelpdeskFilter,
  analysisType = 'desk'
): DatabricksInsightsServiceRequestData => {
  const insightsData = dataArray.sort((a: string[], b: string[]) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });

  const dataFront: DataFrontData = {
    data: [],
    tickets_abertos: [],
    tickets_fechados: [],
    tempo_medio_atendimento: [],
    tempo_medio_espera: [],
    tempo_medio_resposta_atendente: [],
    tempo_medio_fila: [],
    tempo_medio_primeira_resposta_atendente: [],
  };
  insightsData.map((item) => {
    dataFront.data.push(item[0]);
    dataFront.tickets_abertos.push(parseInt(item[1]));
    dataFront.tickets_fechados.push(parseInt(item[2]));
    dataFront.tempo_medio_atendimento.push(item[3]);
    dataFront.tempo_medio_espera.push(item[4]);
    dataFront.tempo_medio_resposta_atendente.push(item[5]);
    dataFront.tempo_medio_fila.push(item[6]);
    dataFront.tempo_medio_primeira_resposta_atendente.push(item[7]);
  });
  const inputs = {
    inputs: {
      dados_front_segmentacao: mapFilterToInsightsRequestFormat(filter, tenantId),
      dados_front: dataFront,
      tipo_analise: analysisType,
    },
  };
  return inputs as DatabricksInsightsServiceRequestData;
};

const mapFilterToInsightsRequestFormat = (filter: HelpdeskFilter, tenantId: string): InsightsSegmentationData => {
  const startDate = filter.startDate ? formatDateForQuery(filter.startDate) : '';
  const endDate = filter.endDate ? formatDateForQuery(filter.endDate) : '';
  const channels = filter.channels?.length > 0 ? filter.channels : [];
  const routerIds = filter.routerIds?.length > 0 ? filter.routerIds : [];
  const botIds = filter.botIds?.length > 0 ? filter.botIds : [];
  const queues = filter.queues?.length > 0 ? filter.queues : [];

  return {
    StartDate: startDate,
    EndDate: endDate,
    Channel: channels,
    RouterId: routerIds,
    BotId: botIds,
    Queues: queues,
    TenantId: tenantId,
    Category: [],
    TemplateName: [],
    CampaignName: [],
  } as InsightsSegmentationData;
};

const addAllConditionsForFilter = (query: string, helpdeskFilter: HelpdeskFilter, dateFieldName = 'data') => {
  query = addCondition(query, getDateCondition(helpdeskFilter.startDate, helpdeskFilter.endDate, dateFieldName));
  query = addCondition(query, checkPropertyAndGetINCondition(helpdeskFilter.channels, CHANNEL_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(helpdeskFilter.routerIds, ROUTER_ID_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(helpdeskFilter.botIds, BOT_ID_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(helpdeskFilter.queues, QUEUE_COLUMN_NAME));
  return query;
};

const checkPropertyAndGetINCondition = (property: string[], columnName: string) => {
  if (property?.length > 0) {
    const propertyString = property.map((item) => `'${item}'`).join(', ');
    return `${columnName} IN (${propertyString})`;
  }
  return '';
};

const addCondition = (query: string, condition: string) => {
  if (condition) {
    return `${query} AND ${condition}`;
  }
  return query;
};

const getDateCondition = (startDate: Date, endDate: Date, dateFieldName = 'data') => {
  if (startDate && endDate) {
    return `${dateFieldName} BETWEEN '${formatDateForQuery(startDate)}' AND '${formatDateForQuery(endDate)}'`;
  }
  return '';
};

export const getBigNumbersQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `WITH tempos_medios AS (
    SELECT
      SUM(tickets_abertos) AS abertos,
      SUM(tickets_fechados) AS fechados,
      SUM(tickets_perdidos) AS perdidos,
      SUM(tickets_abandonados) AS abandonados,
      SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS tme_seg,
      SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS tmr_seg,
      SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) AS tmf_seg,
      SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS tma_seg
    FROM clients_trustedzone.ilab.gold_smb_desk_main_data
    WHERE TenantId = '${tenantId}'
  `;

  query = addAllConditionsForFilter(query, helpdeskFilter);

  return `
    ${query}
    )

    SELECT
      abertos,
      fechados,
      perdidos,
      abandonados,
      ${addCaseForTimeInterval('tme_seg')} AS TME,
      ${addCaseForTimeInterval('tmr_seg')} AS TMR,
      ${addCaseForTimeInterval('tmf_seg')} AS TMF,
      ${addCaseForTimeInterval('tma_seg')} AS TMA
    FROM tempos_medios`;
}

export const getTicketsPanelQueueQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `
    WITH tempos_medios AS (
    SELECT
      fila,
      SUM(tickets_fechados) AS fechados,
      SUM(tickets_perdidos) AS perdidos,
      SUM(tickets_abandonados) AS abandonados,
      SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS tma_seg,
      SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS tme_seg,
      SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS tmr_seg,
      SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS tm1r_seg
    FROM
      clients_trustedzone.ilab.gold_smb_desk_main_data
    WHERE
      TenantId = '${tenantId}'`;
  
  query = addAllConditionsForFilter(query, helpdeskFilter);
  
  return `
    ${query}
    GROUP BY
      fila
    )
    SELECT
      fila,
      fechados,
      perdidos,
      abandonados,
      ${addCaseForTimeInterval('tma_seg')} AS TMA,
      ${addCaseForTimeInterval('tme_seg')} AS TME,
      ${addCaseForTimeInterval('tmr_seg')} AS TMR,
      ${addCaseForTimeInterval('tm1r_seg')} AS TM1R
    FROM
      tempos_medios
    ORDER BY
      fila
  `;
}

const addCaseForTimeInterval = (sqlVariable: string) => {
  return `
    CASE WHEN ${sqlVariable} >= 86400 THEN
      CONCAT(
        CAST(FLOOR(${sqlVariable} / 86400) AS STRING), 'd ',
        LPAD(CAST(FLOOR(MOD(${sqlVariable}, 86400) / 3600) AS STRING), 2, '0'), ':',
        LPAD(CAST(FLOOR(MOD(${sqlVariable}, 3600) / 60) AS STRING), 2, '0')
      )
    ELSE 
      date_format(timestamp_seconds(CAST(${sqlVariable} AS BIGINT)), 'HH:mm:ss')
    END`;
};

export const getTicketsPanelAttendantsQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `WITH tempos_medios AS (
    SELECT
      nome_atendente,
      SUM(tickets_fechados) AS fechados,
      SUM(tickets_perdidos) AS perdidos,
      SUM(tickets_abandonados) AS abandonados,
      SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS tma_seg,
      SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS tme_seg,
      SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS tmr_seg,
      SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS tm1r_seg
    FROM clients_trustedzone.ilab.gold_smb_desk_main_data_atendentes
    WHERE TenantId = '${tenantId}'
      AND nome_atendente <> 'sem_atendente'`;

  query = addAllConditionsForFilter(query, helpdeskFilter);

  return `
    ${query} 
    GROUP BY nome_atendente
    )

    SELECT
      nome_atendente,
      fechados,
      perdidos,
      abandonados,
      ${addCaseForTimeInterval('tma_seg')} AS TMA,
      ${addCaseForTimeInterval('tme_seg')} AS TME,
      ${addCaseForTimeInterval('tmr_seg')} AS TMR,
      ${addCaseForTimeInterval('tm1r_seg')} AS TM1R
    FROM tempos_medios
    ORDER BY nome_atendente`;
}

export const getTicketsPanelTagsQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `
    WITH tempos_medios AS (
    SELECT
      tags,
      SUM(tickets_fechados) AS fechados,
      SUM(tickets_perdidos) AS perdidos,
      SUM(tickets_abandonados) AS abandonados,
      SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS tma_seg,
      SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS tme_seg,
      SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS tmr_seg,
      SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS tm1r_seg
    FROM clients_trustedzone.ilab.gold_smb_desk_main_data_tags
    WHERE TenantId = '${tenantId}'`;
  
  query = addAllConditionsForFilter(query, helpdeskFilter);
  
  return `
    ${query}
      GROUP BY tags
    )

    SELECT
      tags,
      fechados,
      perdidos,
      abandonados,
      ${addCaseForTimeInterval('tma_seg')} AS TMA,
      ${addCaseForTimeInterval('tme_seg')} AS TME,
      ${addCaseForTimeInterval('tmr_seg')} AS TMR,
      ${addCaseForTimeInterval('tm1r_seg')} AS TM1R
    FROM tempos_medios
    ORDER BY tags`
};

export const getPerformanceGraphQuery = (tenantId: string, helpdeskFilter: HelpdeskFilter) => {
  let query = `
    WITH tempos_medios AS (
    SELECT
      data,
      SUM(tickets_abertos) AS abertos,
      SUM(tickets_fechados) AS fechados,
      SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS tma_seg,
      SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS tme_seg,
      SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS tmr_seg,
      SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) AS tmf_seg,
      SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS tm1r_seg
    FROM
      clients_trustedzone.ilab.gold_smb_desk_main_data
    WHERE
      TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, helpdeskFilter);
  
  return `
    ${query}
    GROUP BY
      data
    )
    SELECT
      data,
      abertos,
      fechados,
      ${addCaseForTimeInterval('tma_seg')} AS TMA,
      ${addCaseForTimeInterval('tme_seg')} AS TME,
      ${addCaseForTimeInterval('tmr_seg')} AS TMR,
      ${addCaseForTimeInterval('tmf_seg')} AS TMF,
      ${addCaseForTimeInterval('tm1r_seg')} AS TM1R
    FROM
      tempos_medios
    ORDER BY
      data`;
};

const performanceGraphQuery = `SELECT
  data,
  SUM(tickets_abertos) AS abertos,
  SUM(tickets_fechados) AS fechados,
  date_format(
    timestamp_seconds(CAST(SUM(tempo_atendimento) / NULLIF(SUM(qtd_tempo_atendimento), 0) AS BIGINT)),
    'HH:mm:ss'
  ) AS TMA,
  date_format(
    timestamp_seconds(CAST(SUM(tempo_espera) / NULLIF(SUM(qtd_tempo_espera), 0) AS BIGINT)),
    'HH:mm:ss'
  ) AS TME,
  date_format(
    timestamp_seconds(CAST(SUM(tempo_resposta) / NULLIF(SUM(qtd_tempo_resposta), 0) AS BIGINT)),
    'HH:mm:ss'
  ) AS TMR,
  date_format(
    timestamp_seconds(CAST(SUM(tempo_fila) / NULLIF(SUM(qtd_tempo_fila), 0) AS BIGINT)),
    'HH:mm:ss'
  ) AS TMF,
   date_format(
    timestamp_seconds(CAST(SUM(tempo_primeira_respota) / NULLIF(SUM(qtd_tempo_primeira_respota), 0) AS BIGINT)),
    'HH:mm:ss'
  ) AS TM1R
FROM clients_trustedzone.ilab.gold_smb_desk_main_data
WHERE
  1=1`;

const closingTicketsHeatMap = `SELECT
  CASE dia_semanafechamento
    WHEN 'Sun' THEN 'domingo'
    WHEN 'Mon' THEN 'segunda'
    WHEN 'Tue' THEN 'terça'
    WHEN 'Wed' THEN 'quarta'
    WHEN 'Thu' THEN 'quinta'
    WHEN 'Fri' THEN 'sexta'
    WHEN 'Sat' THEN 'sábado'
  END AS dia_semanafechamento,
  SUM(CASE WHEN hora_fechamento = '00' THEN tickets_fechados ELSE 0 END) AS 00h,
  SUM(CASE WHEN hora_fechamento = '01' THEN tickets_fechados ELSE 0 END) AS 01h,
  SUM(CASE WHEN hora_fechamento = '02' THEN tickets_fechados ELSE 0 END) AS 02h,
  SUM(CASE WHEN hora_fechamento = '03' THEN tickets_fechados ELSE 0 END) AS 03h,
  SUM(CASE WHEN hora_fechamento = '04' THEN tickets_fechados ELSE 0 END) AS 04h,
  SUM(CASE WHEN hora_fechamento = '05' THEN tickets_fechados ELSE 0 END) AS 05h,
  SUM(CASE WHEN hora_fechamento = '06' THEN tickets_fechados ELSE 0 END) AS 06h,
  SUM(CASE WHEN hora_fechamento = '07' THEN tickets_fechados ELSE 0 END) AS 07h,
  SUM(CASE WHEN hora_fechamento = '08' THEN tickets_fechados ELSE 0 END) AS 08h,
  SUM(CASE WHEN hora_fechamento = '09' THEN tickets_fechados ELSE 0 END) AS 09h,
  SUM(CASE WHEN hora_fechamento = '10' THEN tickets_fechados ELSE 0 END) AS 10h,
  SUM(CASE WHEN hora_fechamento = '11' THEN tickets_fechados ELSE 0 END) AS 11h,
  SUM(CASE WHEN hora_fechamento = '12' THEN tickets_fechados ELSE 0 END) AS 12h,
  SUM(CASE WHEN hora_fechamento = '13' THEN tickets_fechados ELSE 0 END) AS 13h,
  SUM(CASE WHEN hora_fechamento = '14' THEN tickets_fechados ELSE 0 END) AS 14h,
  SUM(CASE WHEN hora_fechamento = '15' THEN tickets_fechados ELSE 0 END) AS 15h,
  SUM(CASE WHEN hora_fechamento = '16' THEN tickets_fechados ELSE 0 END) AS 16h,
  SUM(CASE WHEN hora_fechamento = '17' THEN tickets_fechados ELSE 0 END) AS 17h,
  SUM(CASE WHEN hora_fechamento = '18' THEN tickets_fechados ELSE 0 END) AS 18h,
  SUM(CASE WHEN hora_fechamento = '19' THEN tickets_fechados ELSE 0 END) AS 19h,
  SUM(CASE WHEN hora_fechamento = '20' THEN tickets_fechados ELSE 0 END) AS 20h,
  SUM(CASE WHEN hora_fechamento = '21' THEN tickets_fechados ELSE 0 END) AS 21h,
  SUM(CASE WHEN hora_fechamento = '22' THEN tickets_fechados ELSE 0 END) AS 22h,
  SUM(CASE WHEN hora_fechamento = '23' THEN tickets_fechados ELSE 0 END) AS 23h
FROM clients_trustedzone.ilab.gold_smb_desk_main_data_heatmap_fechamento
WHERE 1=1
`;

const openingTicketsHeatMap = `SELECT
  CASE dia_semanacriacao
    WHEN 'Sun' THEN 'domingo'
    WHEN 'Mon' THEN 'segunda'
    WHEN 'Tue' THEN 'terça'
    WHEN 'Wed' THEN 'quarta'
    WHEN 'Thu' THEN 'quinta'
    WHEN 'Fri' THEN 'sexta'
    WHEN 'Sat' THEN 'sábado'
  END AS dia_semanacriacao,
  SUM(CASE WHEN hora_criacao = '00' THEN tickets_fechados ELSE 0 END) AS 00h,
  SUM(CASE WHEN hora_criacao = '01' THEN tickets_fechados ELSE 0 END) AS 01h,
  SUM(CASE WHEN hora_criacao = '02' THEN tickets_fechados ELSE 0 END) AS 02h,
  SUM(CASE WHEN hora_criacao = '03' THEN tickets_fechados ELSE 0 END) AS 03h,
  SUM(CASE WHEN hora_criacao = '04' THEN tickets_fechados ELSE 0 END) AS 04h,
  SUM(CASE WHEN hora_criacao = '05' THEN tickets_fechados ELSE 0 END) AS 05h,
  SUM(CASE WHEN hora_criacao = '06' THEN tickets_fechados ELSE 0 END) AS 06h,
  SUM(CASE WHEN hora_criacao = '07' THEN tickets_fechados ELSE 0 END) AS 07h,
  SUM(CASE WHEN hora_criacao = '08' THEN tickets_fechados ELSE 0 END) AS 08h,
  SUM(CASE WHEN hora_criacao = '09' THEN tickets_fechados ELSE 0 END) AS 09h,
  SUM(CASE WHEN hora_criacao = '10' THEN tickets_fechados ELSE 0 END) AS 10h,
  SUM(CASE WHEN hora_criacao = '11' THEN tickets_fechados ELSE 0 END) AS 11h,
  SUM(CASE WHEN hora_criacao = '12' THEN tickets_fechados ELSE 0 END) AS 12h,
  SUM(CASE WHEN hora_criacao = '13' THEN tickets_fechados ELSE 0 END) AS 13h,
  SUM(CASE WHEN hora_criacao = '14' THEN tickets_fechados ELSE 0 END) AS 14h,
  SUM(CASE WHEN hora_criacao = '15' THEN tickets_fechados ELSE 0 END) AS 15h,
  SUM(CASE WHEN hora_criacao = '16' THEN tickets_fechados ELSE 0 END) AS 16h,
  SUM(CASE WHEN hora_criacao = '17' THEN tickets_fechados ELSE 0 END) AS 17h,
  SUM(CASE WHEN hora_criacao = '18' THEN tickets_fechados ELSE 0 END) AS 18h,
  SUM(CASE WHEN hora_criacao = '19' THEN tickets_fechados ELSE 0 END) AS 19h,
  SUM(CASE WHEN hora_criacao = '20' THEN tickets_fechados ELSE 0 END) AS 20h,
  SUM(CASE WHEN hora_criacao = '21' THEN tickets_fechados ELSE 0 END) AS 21h,
  SUM(CASE WHEN hora_criacao = '22' THEN tickets_fechados ELSE 0 END) AS 22h,
  SUM(CASE WHEN hora_criacao = '23' THEN tickets_fechados ELSE 0 END) AS 23h
FROM clients_trustedzone.ilab.gold_smb_desk_main_data_heatmap_criacao
WHERE 1=1
`;
// aditional filters
// -- Filtro de data
// and data between '2025-03-01' and '2025-03-24'
// -- Filtro de canal
// and Canal IN ('wa.gw.msging.net', '0mn.io')
// -- Filtro de Router
// and RouterId in ('<EMAIL>')
// -- Filtro de BotId
// and BotId IN ('<EMAIL>')
// -- Filtro de Fila
// and Fila IN ('Pixbet - Blipchat')
