import { PerformanceFrontData, ProactiveMessagesFilter, FunnelQueryType } from '@typings/ProactiveMessagesFilter';
import { DatabricksInsightsServiceRequestData, InsightsSegmentationData } from '@services/BlipInsightsService';
import { formatDateForQuery } from '@utils/time';

const CATEGORY_COLUMN_NAME = 'Category';
const TEMPLATE_COLUMN_NAME = 'Template';
const CAMPAIGN_COLUMN_NAME = 'Campaign';
const ROUTER_ID_COLUMN_NAME = 'RouterBotId';
const BOT_ID_COLUMN_NAME = 'BotId';
const DB_BASE = 'rdproduct_trustedzone.ilab.gold_smb_notifications_general_metrics';

const addAllConditionsForFilter = (
  query: string,
  proactiveMessageFilter: ProactiveMessagesFilter,
  dateFieldName = 'Date'
) => {
  query = addCondition(
    query,
    getDateCondition(proactiveMessageFilter.startDate, proactiveMessageFilter.endDate, dateFieldName)
  );
  query = addCondition(query, checkPropertyAndGetINCondition(proactiveMessageFilter.routerIds, ROUTER_ID_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(proactiveMessageFilter.botIds, BOT_ID_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(proactiveMessageFilter.category, CATEGORY_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(proactiveMessageFilter.template, TEMPLATE_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(proactiveMessageFilter.campaign, CAMPAIGN_COLUMN_NAME));
  return query;
};

const checkPropertyAndGetINCondition = (property: string[], columnName: string) => {
  if (property?.length > 0) {
    const propertyString = property.map((item) => `'${item}'`).join(', ');
    return `${columnName} IN (${propertyString})`;
  }
  return '';
};

const addCondition = (query: string, condition: string) => {
  if (condition) {
    return `${query} AND ${condition}`;
  }
  return query;
};

const getDateCondition = (startDate: Date, endDate: Date, dateFieldName = 'Date') => {
  if (startDate && endDate) {
    return `${dateFieldName} BETWEEN '${formatDateForQuery(startDate)}' AND '${formatDateForQuery(endDate)}'`;
  }
  return '';
};

export const getInsightsDataQuery = (
  tenantId: string,
  endDate: Date,
  routerIds: string[],
  botIds: string[],
  campaign: string[],
  template: string[],
  category: string[]
) => {
  const endDateFormatted = formatDateForQuery(endDate);

  const routerIdsCondition = checkPropertyAndGetINCondition(routerIds, 'RouterBotId');
  const botIdsCondition = checkPropertyAndGetINCondition(botIds, 'BotId');
  const campaignsCondition = checkPropertyAndGetINCondition(campaign, 'Campaign');
  const templatesCondition = checkPropertyAndGetINCondition(template, 'Template');
  const categoriesCondition = checkPropertyAndGetINCondition(category, 'Category');

  let query = `
    SELECT
      Date,
      SUM(Sent) AS \`Mensagens enviadas\`,
      SUM(Received) AS \`Mensagens recebidas\`,
      SUM(Answered) AS \`Mensagens respondidas\`,
      ROUND(
        SUM(Answered) / NULLIF(SUM(Received), 0) * 100
      ) AS \`Nível de engajamento\`
    FROM
      ${DB_BASE}
    WHERE
      1=1
      AND TenantId = '${tenantId}'
      AND date_format(Date, 'yyyy-MM') BETWEEN
      (
        CASE
          WHEN date_format('${endDateFormatted}', 'yyyy-MM') >= date_format(current_date(), 'yyyy-MM') THEN trunc(add_months(current_date(), -1), 'MM')
          ELSE trunc('${endDateFormatted}', 'MM')
        END
      ) AND
      (
        CASE
          WHEN date_format('${endDateFormatted}', 'yyyy-MM') >= date_format(current_date(), 'yyyy-MM') THEN last_day(add_months(current_date(), -1))
          ELSE last_day('${endDateFormatted}')
        END
      )
  `;

  query = addCondition(query, routerIdsCondition);
  query = addCondition(query, botIdsCondition);
  query = addCondition(query, campaignsCondition);
  query = addCondition(query, templatesCondition);
  query = addCondition(query, categoriesCondition);

  query = `
    ${query}
    GROUP BY 1
    ORDER BY 1
  `;
  return query;
};

export const mapInsightsToDatabricksInsigthsRequestData = (
  dataArray: string[][],
  tenantId: string,
  filter: ProactiveMessagesFilter,
  analysisType = 'notifications'
): DatabricksInsightsServiceRequestData => {
  const insightsData = dataArray.sort((a: string[], b: string[]) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });

  const dataFront: PerformanceFrontData = {
    data: [],
    sentMessages: [],
    receivedMessages: [],
    answeredMessages: [],
    engagementRate: [],
  };

  insightsData.map((item) => {
    dataFront.data.push(item[0]);
    dataFront.sentMessages.push(parseInt(item[1]));
    dataFront.receivedMessages.push(parseInt(item[2]));
    dataFront.answeredMessages.push(parseInt(item[3]));
    dataFront.engagementRate.push(parseFloat(item[4]));
  });

  const inputs = {
    inputs: {
      dados_front_segmentacao: mapFilterToInsightsRequestFormat(filter, tenantId),
      dados_front: dataFront,
      tipo_analise: analysisType,
    },
  };

  return inputs as DatabricksInsightsServiceRequestData;
};

const mapFilterToInsightsRequestFormat = (
  filter: ProactiveMessagesFilter,
  tenantId: string
): InsightsSegmentationData => {
  const startDate = filter.startDate ? formatDateForQuery(filter.startDate) : '';
  const endDate = filter.endDate ? formatDateForQuery(filter.endDate) : '';
  const routerIds = filter.routerIds?.length > 0 ? filter.routerIds : [];
  const botIds = filter.botIds?.length > 0 ? filter.botIds : [];
  const campaigns = filter.campaign?.length > 0 ? filter.campaign : [];
  const templates = filter.template?.length > 0 ? filter.template : [];
  const categories = filter.category?.length > 0 ? filter.category : [];

  return {
    StartDate: startDate,
    EndDate: endDate,
    RouterId: routerIds,
    BotId: botIds,
    CampaignName: campaigns,
    TemplateName: templates,
    Category: categories,
    TenantId: tenantId,
    Queues: [],
    Channel: [],
  } as InsightsSegmentationData;
};

const getFunnelChartReadMessagesQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    SUM(Consumed) AS Lidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChartRespondedMessagesQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    SUM(Answered) AS Respondidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelCharSentMessagesQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    SUM(Sent) AS Enviadas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChaReceivedMessagesQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    SUM(Received) AS Recebidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChartConversionQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    SUM(Conversion) AS Conversao
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

export const getFunnelChartQueries = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return {
    sent: {
      bars: getFunnelChartBarsQuery(tenantId, filter, 'enviadas'),
      indicators: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-de-falhas'),
      variation: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-comparativo-de-falhas'),
    },
    received: {
      bars: getFunnelChartBarsQuery(tenantId, filter, 'recebidas'),
      indicators: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-de-recebimento'),
      variation: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-comparativo-de-recebimento'),
    },
    read: {
      bars: getFunnelChartBarsQuery(tenantId, filter, 'lidas'),
      indicators: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-de-leitura'),
      variation: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-comparativo-de-leitura'),
    },
    responded: {
      bars: getFunnelChartBarsQuery(tenantId, filter, 'respondidas'),
      indicators: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-de-resposta'),
      variation: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-comparativo-de-resposta'),
    },
    conversion: {
      bars: getFunnelChartBarsQuery(tenantId, filter, 'conversão'),
      // indicators: getFunnelChartIndicatorQuery(tenantId, filter, 'percentual-de-falhas'),
    },
  };
};

export const getFunnelChartTooltipsQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  const q = `
  SELECT
    ROUND(100.0 * SUM(Sent) / SUM(Sent), 2) AS Enviadas,
    ROUND(100.0 * SUM(Received) / SUM(Sent), 2) AS Recebidas,
    ROUND(100.0 * SUM(Consumed) / SUM(Sent), 2) AS Consumidas,
    ROUND(100.0 * SUM(Answered) / SUM(Sent), 2) AS Respondidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
  return ` ${addAllConditionsForFilter(q, filter, 'Date')}`.trim();
};

// prettier-ignore
export const getFunnelChartBarsQuery = (tenantId: string, filter: ProactiveMessagesFilter, queryType: FunnelQueryType) => {
  let query = '';
  switch (queryType) {
  case 'enviadas':
    query = getFunnelCharSentMessagesQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'recebidas':
    query = getFunnelChaReceivedMessagesQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'lidas':
    query = getFunnelChartReadMessagesQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'respondidas':
    query = getFunnelChartRespondedMessagesQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'conversão':
    query = getFunnelChartConversionQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'tooltips':
    query = getFunnelChartTooltipsQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  default:
    return '';
  }
};

// prettier-ignore
export const getFunnelChartIndicatorQuery = (tenantId: string, filter: ProactiveMessagesFilter, queryType: FunnelQueryType) => {
  let query = '';
  switch (queryType) {
  case 'percentual-de-falhas':
    query = getFunnelChartFailurePercentageQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'percentual-comparativo-de-falhas':
    query = getFunnelChartComparativeFailurePercentageQuery(tenantId, filter);
    return query;
  case 'percentual-de-recebimento':
    query = getFunnelChartReceivedPercentageQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'percentual-comparativo-de-recebimento':
    query = getFunnelChartComparativeReceivedPercentageQuery(tenantId, filter);
    return query;
  case 'percentual-de-leitura':
    query = getFunnelChartReadPercentageQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'percentual-comparativo-de-leitura':
    query = getFunnelChartComparativeReadPercentageQuery(tenantId, filter);
    return query;
  case 'percentual-de-resposta':
    query = getFunnelChartAnswerPercentageQuery(tenantId, filter);
    return ` ${addAllConditionsForFilter(query, filter, 'Date')}`.trim();
  case 'percentual-comparativo-de-resposta':
    query = getFunnelChartComparativeAsnweredPercentageQuery(tenantId, filter);
    return query;
  default:
    return '';
  }
};

const getFunnelChartComparativeAsnweredPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return /*sql*/ `
WITH parametros AS (
SELECT
  DATE('${formatDateForQuery(filter.startDate)}') AS data_inicio,
  DATE('${formatDateForQuery(filter.endDate)}') AS data_fim
),
datas AS (
  SELECT
    data_inicio,
    data_fim,
    DATE_SUB(data_inicio, DATEDIFF(data_fim, data_inicio) + 1) AS inicio_anterior,
    DATE_SUB(data_inicio, 1) AS fim_anterior
  FROM
    parametros
),
agregado AS (
  SELECT
    CASE
      WHEN Date BETWEEN d.data_inicio AND d.data_fim THEN 'atual'
      WHEN Date BETWEEN d.inicio_anterior AND d.fim_anterior THEN 'anterior'
    END AS periodo,
    SUM(Received) AS Received,
    SUM(Answered) AS Answered
  FROM
    ${DB_BASE} m CROSS JOIN datas d
  WHERE
    m.TenantId = '${tenantId}'
    AND (
      m.Date  BETWEEN d.data_inicio AND d.data_fim
      OR m.Date BETWEEN d.inicio_anterior AND d.fim_anterior
    )
  GROUP BY
    periodo
)
SELECT
  MAX(
    CASE
      WHEN periodo = 'atual' THEN ROUND((Answered / Received) * 100)
    END
  ) AS rate,
  ROUND(
    MAX(
      CASE
        WHEN periodo = 'atual' THEN ROUND((Answered / Received) * 100)
      END
    )
    - MAX(
      CASE
        WHEN periodo = 'anterior' THEN ROUND((Answered / Received) * 100)
      END
    ),
    2
  ) AS variation
FROM
  agregado;
`.trim();
};

const getFunnelChartComparativeReadPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return /*sql*/ `
WITH parametros AS (
SELECT
  DATE('${formatDateForQuery(filter.startDate)}') AS data_inicio,
  DATE('${formatDateForQuery(filter.endDate)}') AS data_fim
),
datas AS (
  SELECT
    data_inicio,
    data_fim,
    DATE_SUB(data_inicio, DATEDIFF(data_fim, data_inicio) + 1) AS inicio_anterior,
    DATE_SUB(data_inicio, 1) AS fim_anterior
  FROM
    parametros
),
agregado AS (
  SELECT
    CASE
      WHEN Date BETWEEN d.data_inicio AND d.data_fim THEN 'atual'
      WHEN Date BETWEEN d.inicio_anterior AND d.fim_anterior THEN 'anterior'
    END AS periodo,
    SUM(Received) AS Received,
    SUM(Consumed) AS Consumed
  FROM
    ${DB_BASE} m CROSS JOIN datas d
  WHERE
    m.TenantId = '${tenantId}'
    AND (
      m.Date  BETWEEN d.data_inicio AND d.data_fim
      OR m.Date BETWEEN d.inicio_anterior AND d.fim_anterior
    )
  GROUP BY
    periodo
)
SELECT
  MAX(
    CASE
      WHEN periodo = 'atual' THEN ROUND((Consumed / Received) * 100)
    END
  ) AS rate,
  ROUND(
    MAX(
      CASE
        WHEN periodo = 'atual' THEN ROUND((Consumed / Received) * 100)
      END
    )
    - MAX(
      CASE
        WHEN periodo = 'anterior' THEN ROUND((Consumed / Received) * 100)
      END
    ),
    2
  ) AS variation
FROM
  agregado;
`.trim();
};

const getFunnelChartComparativeReceivedPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return /*sql*/ `
WITH parametros AS (
SELECT
  DATE('${formatDateForQuery(filter.startDate)}') AS data_inicio,
  DATE('${formatDateForQuery(filter.endDate)}') AS data_fim
),
datas AS (
  SELECT
    data_inicio,
    data_fim,
    DATE_SUB(data_inicio, DATEDIFF(data_fim, data_inicio) + 1) AS inicio_anterior,
    DATE_SUB(data_inicio, 1) AS fim_anterior
  FROM
    parametros
),
agregado AS (
  SELECT
    CASE
      WHEN Date BETWEEN d.data_inicio AND d.data_fim THEN 'atual'
      WHEN Date BETWEEN d.inicio_anterior AND d.fim_anterior THEN 'anterior'
    END AS periodo,
    SUM(Sent) AS Sent,
    SUM(Received) AS Received
  FROM
    ${DB_BASE} m CROSS JOIN datas d
  WHERE
    m.TenantId = '${tenantId}'
    AND (
      m.Date  BETWEEN d.data_inicio AND d.data_fim
      OR m.Date BETWEEN d.inicio_anterior AND d.fim_anterior
    )
  GROUP BY
    periodo
)
SELECT
  MAX(
    CASE
      WHEN periodo = 'atual' THEN ROUND((Received / Sent) * 100)
    END
  ) AS rate,
  ROUND(
    MAX(
      CASE
        WHEN periodo = 'atual' THEN ROUND((Received / Sent) * 100)
      END
    )
    - MAX(
      CASE
        WHEN periodo = 'anterior' THEN ROUND((Received / Sent) * 100)
      END
    ),
    2
  ) 
AS variation
FROM
  agregado;
`.trim();
};

const getFunnelChartComparativeFailurePercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return /*sql*/ `
WITH parametros AS (
SELECT
  DATE('${formatDateForQuery(filter.startDate)}') AS data_inicio,
  DATE('${formatDateForQuery(filter.endDate)}') AS data_fim
),
datas AS (
  SELECT
    data_inicio,
    data_fim,
    DATE_SUB(data_inicio, DATEDIFF(data_fim, data_inicio) + 1) AS inicio_anterior,
    DATE_SUB(data_inicio, 1) AS fim_anterior
  FROM
    parametros
),
agregado AS (
  SELECT
    CASE
      WHEN Date BETWEEN d.data_inicio AND d.data_fim THEN 'atual'
      WHEN Date BETWEEN d.inicio_anterior AND d.fim_anterior THEN 'anterior'
    END AS periodo,
    SUM(Total) AS Total,
    SUM(Failed) AS Failed
  FROM
    ${DB_BASE} m CROSS JOIN datas d
  WHERE
    m.TenantId = '${tenantId}'
    AND (
      m.Date  BETWEEN d.data_inicio AND d.data_fim
      OR m.Date BETWEEN d.inicio_anterior AND d.fim_anterior
    )
  GROUP BY
    periodo
)
-- Compara os dois períodos
SELECT
  MAX(
    CASE
      WHEN periodo = 'atual' THEN ROUND((Failed / Total) * 100)
    END
  ) AS failure_rate,
  ROUND(
    MAX(
      CASE
        WHEN periodo = 'atual' THEN ROUND((Failed / Total) * 100)
      END
    )
    - MAX(
      CASE
        WHEN periodo = 'anterior' THEN ROUND((Failed / Total) * 100)
      END
    ),
    2
  ) AS variation
FROM
  agregado;
`.trim();
};

const getFunnelChartFailurePercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    ROUND(SUM(Failed) / SUM(Total) * 100)
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChartReceivedPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    ROUND(SUM(Received) / SUM(Sent) * 100)
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChartReadPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    ROUND(SUM(Consumed) / SUM(Received) * 100) 
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

const getFunnelChartAnswerPercentageQuery = (tenantId: string, filter: ProactiveMessagesFilter) => {
  return `
  SELECT
    ROUND(SUM(Answered) / SUM(Received) * 100) 
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'
  `;
};

export const getTotalUsersResponseQuery = (tenantId: string, proactiveMessagesFilter: ProactiveMessagesFilter) => {
  let query = `
    WITH base AS (
      SELECT *
      FROM rdproduct_trustedzone.ilab.gold_smb_notifications_user_responses
      WHERE TenantId = '${tenantId}'
  `;

  query = addAllConditionsForFilter(query, proactiveMessagesFilter);

  query = /*sql*/ `
    ${query}
    ),
    respostas_agrupadas AS (
      SELECT
        UserResponseContent AS \`Texto de resposta\`,
        SUM(Answered) AS TotalRespostas,
        SUM(AnswerTime) AS SomaTempoResposta
      FROM base
      GROUP BY UserResponseContent
    ),
    total_respostas AS (
      SELECT SUM(Answered) AS TotalGeral FROM base
    )
    SELECT
      r.\`Texto de resposta\`,
      r.TotalRespostas,
      ROUND(r.TotalRespostas / t.TotalGeral * 100) AS \`Taxa de resposta\`,
      CASE
        WHEN (r.SomaTempoResposta / r.TotalRespostas) >= 86400 THEN
          CONCAT(
            FLOOR(r.SomaTempoResposta / r.TotalRespostas / 86400), 'd ',
            LPAD(FLOOR(MOD(r.SomaTempoResposta / r.TotalRespostas, 86400) / 3600), 2, '0'), ':',
            LPAD(FLOOR(MOD(r.SomaTempoResposta / r.TotalRespostas, 3600) / 60), 2, '0'), ':',
            LPAD(FLOOR(MOD(r.SomaTempoResposta / r.TotalRespostas, 60)), 2, '0')
          )
        ELSE
          CONCAT(
            LPAD(FLOOR(r.SomaTempoResposta / r.TotalRespostas / 3600), 2, '0'), ':',
            LPAD(FLOOR(MOD(r.SomaTempoResposta / r.TotalRespostas, 3600) / 60), 2, '0'), ':',
            LPAD(FLOOR(MOD(r.SomaTempoResposta / r.TotalRespostas, 60)), 2, '0')
          )
      END AS \`Tempo médio de resposta\`
    FROM respostas_agrupadas r
    CROSS JOIN total_respostas t
    ORDER BY r.TotalRespostas DESC
  `;
  return query;
};

export const getMessageModelFailureQuery = (tenantId: string, proactiveMessagesFilter: ProactiveMessagesFilter) => {
  let query = `
    WITH falhas_agrupadas AS (
      SELECT
        FailureType,
        SUM(TotalFailures) AS total_falhas
      FROM
        rdproduct_trustedzone.ilab.gold_smb_notifications_message_template_failures
      WHERE
        TenantId = '${tenantId}'
  `;

  query = addAllConditionsForFilter(query, proactiveMessagesFilter);

  query = /*sql*/ `
    ${query}
        AND FailureType IS NOT NULL
      GROUP BY
        FailureType
    ),
    total_enviadas AS (
      SELECT
        SUM(Total) AS total_geral
      FROM
        rdproduct_trustedzone.ilab.gold_smb_notifications_message_template_failures
      WHERE
        TenantId = '${tenantId}'
  `;

  query = addAllConditionsForFilter(query, proactiveMessagesFilter);

  query = /*sql*/ `
    ${query}
    )
    SELECT
      f.FailureType AS TipoDeFalha,
      f.total_falhas AS TotalDeFalhas,
      ROUND(f.total_falhas / t.total_geral * 100) AS TaxaDeFalhas
    FROM
      falhas_agrupadas f
    CROSS JOIN
      total_enviadas t
    ORDER BY
      TotalDeFalhas DESC
    LIMIT 10
  `;
  return query;
};

export const getActiveMessagePerformanceQuery = (
  tenantId: string,
  proactiveMessagesFilter: ProactiveMessagesFilter
) => {
  let query = `
    SELECT
      Template AS \`Nome do modelo\`,
      SUM(Sent) AS Enviadas,
      ROUND((SUM(Received) / SUM(Sent)) * 100) AS \`Taxa de recebimento\`,
      ROUND((SUM(Consumed) / SUM(Received)) * 100) AS \`Taxa de leitura\`,
      ROUND((SUM(Answered) / SUM(Received)) * 100) AS \`Taxa de respostas\`,
      ROUND((SUM(Failed) / SUM(Total)) * 100) AS \`Taxa de falhas\`
    FROM
      ${DB_BASE}
    WHERE
      TenantId = '${tenantId}'
  `;

  query = addAllConditionsForFilter(query, proactiveMessagesFilter);

  query = /*sql*/ `
    ${query}
    GROUP BY
      Template
  `;
  return query;
};

export const getInteractionTypesChartQuery = (tenantId: string, proactiveMessageFilter: ProactiveMessagesFilter) => {
  const parcialQuery = `SELECT
    InteractionType,
    SUM(Answered) AS TotalRespondidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = \'${tenantId}\'
    ${addAllConditionsForFilter('', proactiveMessageFilter, 'Date')}
  GROUP BY
    InteractionType`;
  let query = `WITH respostas AS (${parcialQuery}),
total AS (
  SELECT
    SUM(TotalRespondidas) AS total_geral
  FROM
    respostas
)
SELECT
  r.InteractionType AS \`Tipos de interação\`,
  TotalRespondidas AS respostas,
  ROUND(r.TotalRespondidas / t.total_geral * 100) AS Percentual
FROM
  respostas r CROSS JOIN total t
`;
  query = `${query}ORDER BY Percentual DESC`;
  return query;
};

export const getAnswerTypeChartQuery = (tenantId: string, proactiveMessageFilter: ProactiveMessagesFilter) => {
  const query = `WITH respostas AS (
  SELECT
    AnswerType,
    SUM(Answered) AS TotalRespondidas
  FROM
    ${DB_BASE}
  WHERE
    TenantId = \'${tenantId}\'
    ${addAllConditionsForFilter('', proactiveMessageFilter, 'Date')}
  GROUP BY
    AnswerType
),
total AS (
  SELECT
    SUM(TotalRespondidas) AS total_geral
  FROM
    respostas
)
SELECT
  r.AnswerType AS \`Tipo de resposta\`,
  TotalRespondidas AS respostas,
  ROUND(r.TotalRespondidas / t.total_geral * 100) AS Percentual
FROM
  respostas r CROSS JOIN total t
ORDER BY
  Percentual DESC`;
  return query;
};

export const getEngagementHeatmapReceiptQuery = (tenantId: string, proactiveMessageFilter: ProactiveMessagesFilter) => {
  let query = `SELECT
  FullWeekDay AS \`Dia da semana\`,
  COALESCE(SUM(CASE WHEN Hour = 0 THEN Received END), 0) AS \`0\`,
  COALESCE(SUM(CASE WHEN Hour = 1 THEN Received END), 0) AS \`1\`,
  COALESCE(SUM(CASE WHEN Hour = 2 THEN Received END), 0) AS \`2\`,
  COALESCE(SUM(CASE WHEN Hour = 3 THEN Received END), 0) AS \`3\`,
  COALESCE(SUM(CASE WHEN Hour = 4 THEN Received END), 0) AS \`4\`,
  COALESCE(SUM(CASE WHEN Hour = 5 THEN Received END), 0) AS \`5\`,
  COALESCE(SUM(CASE WHEN Hour = 6 THEN Received END), 0) AS \`6\`,
  COALESCE(SUM(CASE WHEN Hour = 7 THEN Received END), 0) AS \`7\`,
  COALESCE(SUM(CASE WHEN Hour = 8 THEN Received END), 0) AS \`8\`,
  COALESCE(SUM(CASE WHEN Hour = 9 THEN Received END), 0) AS \`9\`,
  COALESCE(SUM(CASE WHEN Hour = 10 THEN Received END), 0) AS \`10\`,
  COALESCE(SUM(CASE WHEN Hour = 11 THEN Received END), 0) AS \`11\`,
  COALESCE(SUM(CASE WHEN Hour = 12 THEN Received END), 0) AS \`12\`,
  COALESCE(SUM(CASE WHEN Hour = 13 THEN Received END), 0) AS \`13\`,
  COALESCE(SUM(CASE WHEN Hour = 14 THEN Received END), 0) AS \`14\`,
  COALESCE(SUM(CASE WHEN Hour = 15 THEN Received END), 0) AS \`15\`,
  COALESCE(SUM(CASE WHEN Hour = 16 THEN Received END), 0) AS \`16\`,
  COALESCE(SUM(CASE WHEN Hour = 17 THEN Received END), 0) AS \`17\`,
  COALESCE(SUM(CASE WHEN Hour = 18 THEN Received END), 0) AS \`18\`,
  COALESCE(SUM(CASE WHEN Hour = 19 THEN Received END), 0) AS \`19\`,
  COALESCE(SUM(CASE WHEN Hour = 20 THEN Received END), 0) AS \`20\`,
  COALESCE(SUM(CASE WHEN Hour = 21 THEN Received END), 0) AS \`21\`,
  COALESCE(SUM(CASE WHEN Hour = 22 THEN Received END), 0) AS \`22\`,
  COALESCE(SUM(CASE WHEN Hour = 23 THEN Received END), 0) AS \`23\`
FROM rdproduct_trustedzone.ilab.gold_smb_notifications_engagement_volume
WHERE TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, proactiveMessageFilter, 'Date');
  query = `${query}
  GROUP BY FullWeekDay
  ORDER BY
  CASE FullWeekDay
    WHEN 'Domingo' THEN 1
    WHEN 'Segunda' THEN 2
    WHEN 'Terça' THEN 3
    WHEN 'Quarta' THEN 4
    WHEN 'Quinta' THEN 5
    WHEN 'Sexta' THEN 6
    WHEN 'Sábado' THEN 7
    ELSE 8
  END`;
  return query;
};

export const getEngagementHeatmapReadQuery = (tenantId: string, proactiveMessageFilter: ProactiveMessagesFilter) => {
  let query = `SELECT
  FullWeekDay AS \`Dia da semana\`,
  COALESCE(SUM(CASE WHEN Hour = 0 THEN Consumed END), 0) AS \`0\`,
  COALESCE(SUM(CASE WHEN Hour = 1 THEN Consumed END), 0) AS \`1\`,
  COALESCE(SUM(CASE WHEN Hour = 2 THEN Consumed END), 0) AS \`2\`,
  COALESCE(SUM(CASE WHEN Hour = 3 THEN Consumed END), 0) AS \`3\`,
  COALESCE(SUM(CASE WHEN Hour = 4 THEN Consumed END), 0) AS \`4\`,
  COALESCE(SUM(CASE WHEN Hour = 5 THEN Consumed END), 0) AS \`5\`,
  COALESCE(SUM(CASE WHEN Hour = 6 THEN Consumed END), 0) AS \`6\`,
  COALESCE(SUM(CASE WHEN Hour = 7 THEN Consumed END), 0) AS \`7\`,
  COALESCE(SUM(CASE WHEN Hour = 8 THEN Consumed END), 0) AS \`8\`,
  COALESCE(SUM(CASE WHEN Hour = 9 THEN Consumed END), 0) AS \`9\`,
  COALESCE(SUM(CASE WHEN Hour = 10 THEN Consumed END), 0) AS \`10\`,
  COALESCE(SUM(CASE WHEN Hour = 11 THEN Consumed END), 0) AS \`11\`,
  COALESCE(SUM(CASE WHEN Hour = 12 THEN Consumed END), 0) AS \`12\`,
  COALESCE(SUM(CASE WHEN Hour = 13 THEN Consumed END), 0) AS \`13\`,
  COALESCE(SUM(CASE WHEN Hour = 14 THEN Consumed END), 0) AS \`14\`,
  COALESCE(SUM(CASE WHEN Hour = 15 THEN Consumed END), 0) AS \`15\`,
  COALESCE(SUM(CASE WHEN Hour = 16 THEN Consumed END), 0) AS \`16\`,
  COALESCE(SUM(CASE WHEN Hour = 17 THEN Consumed END), 0) AS \`17\`,
  COALESCE(SUM(CASE WHEN Hour = 18 THEN Consumed END), 0) AS \`18\`,
  COALESCE(SUM(CASE WHEN Hour = 19 THEN Consumed END), 0) AS \`19\`,
  COALESCE(SUM(CASE WHEN Hour = 20 THEN Consumed END), 0) AS \`20\`,
  COALESCE(SUM(CASE WHEN Hour = 21 THEN Consumed END), 0) AS \`21\`,
  COALESCE(SUM(CASE WHEN Hour = 22 THEN Consumed END), 0) AS \`22\`,
  COALESCE(SUM(CASE WHEN Hour = 23 THEN Consumed END), 0) AS \`23\`
FROM rdproduct_trustedzone.ilab.gold_smb_notifications_engagement_volume
WHERE TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, proactiveMessageFilter, 'Date');
  query = `${query}
  GROUP BY FullWeekDay
  ORDER BY
  CASE FullWeekDay
    WHEN 'Domingo' THEN 1
    WHEN 'Segunda' THEN 2
    WHEN 'Terça' THEN 3
    WHEN 'Quarta' THEN 4
    WHEN 'Quinta' THEN 5
    WHEN 'Sexta' THEN 6
    WHEN 'Sábado' THEN 7
    ELSE 8
  END`;
  return query;
};

export const getEngagementHeatmapAnsweredQuery = (
  tenantId: string,
  proactiveMessageFilter: ProactiveMessagesFilter
) => {
  let query = `SELECT
  FullWeekDay AS \`Dia da semana\`,
  COALESCE(SUM(CASE WHEN Hour = 0 THEN Answered END), 0) AS \`0\`,
  COALESCE(SUM(CASE WHEN Hour = 1 THEN Answered END), 0) AS \`1\`,
  COALESCE(SUM(CASE WHEN Hour = 2 THEN Answered END), 0) AS \`2\`,
  COALESCE(SUM(CASE WHEN Hour = 3 THEN Answered END), 0) AS \`3\`,
  COALESCE(SUM(CASE WHEN Hour = 4 THEN Answered END), 0) AS \`4\`,
  COALESCE(SUM(CASE WHEN Hour = 5 THEN Answered END), 0) AS \`5\`,
  COALESCE(SUM(CASE WHEN Hour = 6 THEN Answered END), 0) AS \`6\`,
  COALESCE(SUM(CASE WHEN Hour = 7 THEN Answered END), 0) AS \`7\`,
  COALESCE(SUM(CASE WHEN Hour = 8 THEN Answered END), 0) AS \`8\`,
  COALESCE(SUM(CASE WHEN Hour = 9 THEN Answered END), 0) AS \`9\`,
  COALESCE(SUM(CASE WHEN Hour = 10 THEN Answered END), 0) AS \`10\`,
  COALESCE(SUM(CASE WHEN Hour = 11 THEN Answered END), 0) AS \`11\`,
  COALESCE(SUM(CASE WHEN Hour = 12 THEN Answered END), 0) AS \`12\`,
  COALESCE(SUM(CASE WHEN Hour = 13 THEN Answered END), 0) AS \`13\`,
  COALESCE(SUM(CASE WHEN Hour = 14 THEN Answered END), 0) AS \`14\`,
  COALESCE(SUM(CASE WHEN Hour = 15 THEN Answered END), 0) AS \`15\`,
  COALESCE(SUM(CASE WHEN Hour = 16 THEN Answered END), 0) AS \`16\`,
  COALESCE(SUM(CASE WHEN Hour = 17 THEN Answered END), 0) AS \`17\`,
  COALESCE(SUM(CASE WHEN Hour = 18 THEN Answered END), 0) AS \`18\`,
  COALESCE(SUM(CASE WHEN Hour = 19 THEN Answered END), 0) AS \`19\`,
  COALESCE(SUM(CASE WHEN Hour = 20 THEN Answered END), 0) AS \`20\`,
  COALESCE(SUM(CASE WHEN Hour = 21 THEN Answered END), 0) AS \`21\`,
  COALESCE(SUM(CASE WHEN Hour = 22 THEN Answered END), 0) AS \`22\`,
  COALESCE(SUM(CASE WHEN Hour = 23 THEN Answered END), 0) AS \`23\`
FROM rdproduct_trustedzone.ilab.gold_smb_notifications_engagement_volume
WHERE TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, proactiveMessageFilter, 'Date');
  query = `${query}
  GROUP BY FullWeekDay
  ORDER BY
  CASE FullWeekDay
    WHEN 'Domingo' THEN 1
    WHEN 'Segunda' THEN 2
    WHEN 'Terça' THEN 3
    WHEN 'Quarta' THEN 4
    WHEN 'Quinta' THEN 5
    WHEN 'Sexta' THEN 6
    WHEN 'Sábado' THEN 7
    ELSE 8
  END`;
  return query;
};

export const getPerformanceChartQuery = (
  tenantId: string,
  proactiveMessageFilter: ProactiveMessagesFilter) => {
  let query = `
  SELECT
    Date,
    SUM(Sent) AS \`Mensagens enviadas\`,
    SUM(Received) AS \`Mensagens recebidas\`,
    SUM(Answered) AS \`Mensagens respondidas\`,
    ROUND(SUM(Answered) / NULLIF(SUM(Received), 0)  * 100) AS \`Nível de engajamento\`
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, proactiveMessageFilter);
  query = `
    ${query}
  GROUP BY
    Date
  ORDER BY
    Date;
    `;
  return query.trim();
};

export const getPercentageAnswerQuery = (
  tenantId: string,
  proactiveMessageFilter: ProactiveMessagesFilter
) => {
  let query = `
  SELECT
    Date,
    ROUND(SUM(Answered) / SUM(Received) * 100) AS \`% de respostas\`
  FROM
    ${DB_BASE}
  WHERE
    TenantId = '${tenantId}'`;
  query = addAllConditionsForFilter(query, proactiveMessageFilter);
  query = `
    ${query}
  GROUP BY
    Date
  ORDER BY
    Date;
    `;
  return query.trim();
};
