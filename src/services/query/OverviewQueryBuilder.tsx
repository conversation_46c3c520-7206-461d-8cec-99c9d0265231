import { InsightsSegmentationData } from '@services/BlipInsightsService';
import {
  InsightBaseData,
  OverviewFilter,
  DatabricksInsightsServiceRequestData,
  MessagesInsightFrontData,
  UsersInsightFrontData,
} from '@typings/OverviewFilter';
import { formatDateForQuery } from '@utils/time';

const CHANNEL_COLUMN_NAME = 'Canal';
const ROUTER_ID_COLUMN_NAME = 'RouterBotId';
const BOT_ID_COLUMN_NAME = 'BotId';

const addAllConditionsForFilter = (query: string, overviewFilter: OverviewFilter, dateFieldName = 'data') => {
  query = addCondition(query, getDateCondition(overviewFilter.startDate, overviewFilter.endDate, dateFieldName));
  query = addCondition(query, checkPropertyAndGetINCondition(overviewFilter.channels, CHANNEL_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(overviewFilter.routerIds, ROUTER_ID_COLUMN_NAME));
  query = addCondition(query, checkPropertyAndGetINCondition(overviewFilter.botIds, BOT_ID_COLUMN_NAME));
  return query;
};

const checkPropertyAndGetINCondition = (property: string[], columnName: string) => {
  if (property?.length > 0) {
    const propertyString = property.map((item) => `'${item}'`).join(', ');
    return `${columnName} IN (${propertyString})`;
  }
  return '';
};

const addCondition = (query: string, condition: string) => {
  if (condition) {
    return `${query} AND ${condition}`;
  }
  return query;
};

const getDateCondition = (startDate: Date, endDate: Date, dateFieldName = 'data') => {
  if (startDate && endDate) {
    return `${dateFieldName} BETWEEN '${formatDate(startDate)}' AND '${formatDate(endDate)}'`;
  }
  return '';
};

const formatDate = (date: Date): string => {
  // format to yyyy-mm-dd
  const year = date.getFullYear();
  let month = String(date.getMonth() + 1).padStart(2, '0');
  let day = String(date.getDate()).padStart(2, '0');

  if (month.length < 2) {
    month = '0' + month;
  }

  if (day.length < 2) {
    day = '0' + day;
  }

  return `${year}-${month}-${day}`;
};

export const getBigNumbersQuery = (tenantId: string, overviewFilter: OverviewFilter) => {
  let query = /*sql*/ `
SELECT
  SUM(UsuariosAtivos) AS UsuariosAtivos,
  ROUND(SUM(UsuariosEngajados)/SUM(UsuariosAtivos)*100,2) AS Engajamento,
  ROUND(SUM(UsuariosRecorrentes)/SUM(UsuariosAtivos)*100,2) AS Recorrencia,
  SUM(MensagensTotal) AS MensagensTotal,
  ROUND(SUM(MensagensUsuarios)/SUM(UsuariosEngajados),2) AS MensagensPorUsuario,
  SUM(MensagensAtivas) as MensagensAtivas
FROM clients_trustedzone.ilab.gold_smb_overview_data
WHERE 1=1 AND TenantId = \'${tenantId}\'`;

  query = addAllConditionsForFilter(query, overviewFilter);
  return query.trim();
};

export const getBigNumbersPreviousPeriodQuery = (tenantId: string, overviewFilter: OverviewFilter) => {
  const millisecondsInADay = 1000 * 60 * 60 * 24;
  const millisDifference = overviewFilter.endDate.getTime() - overviewFilter.startDate.getTime();
  // assure that currentPeriodInDays is at least 1 day
  const currentPeriodInDays = millisDifference ? millisDifference / millisecondsInADay : 1;
  const selectedFilterCopy = Object.assign({}, overviewFilter);
  selectedFilterCopy.endDate = overviewFilter.startDate;
  const startDate = new Date(overviewFilter.startDate.getTime() - currentPeriodInDays * millisecondsInADay);
  selectedFilterCopy.startDate = startDate;
  return getBigNumbersQuery(tenantId, selectedFilterCopy);
};

export const getUsersGraphQuery = (tenantId: string, overviewFilter: OverviewFilter) => {
  let query = /*sql*/ `
  SELECT
    Data,
    SUM(UsuariosAtivos) AS UsuariosAtivos,
    SUM(UsuariosEngajados) as UsuariosEngajados,
    ROUND(SUM(UsuariosEngajados)/SUM(UsuariosAtivos)*100,2) AS Engajamento,
    ROUND(SUM(UsuariosRecorrentes)/SUM(UsuariosAtivos)*100,2) AS Recorrencia
  FROM clients_trustedzone.ilab.gold_smb_overview_data
  WHERE 1=1 AND TenantId = \'${tenantId}\'`;

  query = addAllConditionsForFilter(query, overviewFilter);
  query = /*sql*/ `
  ${query}
  GROUP BY 1
  ORDER BY 1
  `;
  return query.trim();
};

export const getMessagesGraphQuery = (tenantId: string, overviewFilter: OverviewFilter) => {
  let query = /*sql*/ `
  SELECT
    Data,
    SUM(MensagensBots) AS MensagensEnviadas,
    SUM(MensagensUsuarios) AS MensagensRecebidAS,
    SUM(MensagensAtivas) AS MensagensAtivas,
    ROUND(SUM(MensagensUsuarios)/SUM(UsuariosEngajados),2) AS MensagensPorUsuario
  FROM clients_trustedzone.ilab.gold_smb_overview_data
  WHERE 1=1 AND TenantId = \'${tenantId}\'`;

  query = addAllConditionsForFilter(query, overviewFilter);
  query = /*sql*/ `
  ${query}
  GROUP BY 1
  ORDER BY 1
  `;
  return query.trim();
};

/**
 * Gets the messages graph insights data from the database for the last closed month.
 *
 * Uses the endDate to determine the last closed month and filters data accordingly.
 * Returns data grouped and ordered by date that will be used as parameters for insights.
 *
 * @param tenantId - The ID of the tenant
 * @param endDate - The end date used to determine the query period
 * @returns SQL query string
 */
export const getMessagesGraphInsightsDataQuery = (tenantId: string, endDate: Date) => {
  const endDateFormatted = formatDateForQuery(endDate);
  return `
  SELECT
    Data,
    SUM(UsuariosAtivos) AS UsuariosAtivos,
    SUM(UsuariosEngajados) AS UsuariosEngajados,
    ROUND(
      SUM(UsuariosRecorrentes) / SUM(UsuariosAtivos) * 100,
      2
    ) AS Recorrencia,
    ROUND(
      SUM(UsuariosEngajados) / SUM(UsuariosAtivos) * 100,
      2
    ) AS Engajamento
  FROM
    clients_trustedzone.ilab.gold_smb_overview_data
  WHERE
    1 = 1
    AND TenantId = '${tenantId}'
    AND date_format (Data, 'yyyy-MM') BETWEEN (
      CASE
        WHEN date_format ('${endDateFormatted}', 'yyyy-MM') >= date_format(current_date(), 'yyyy-MM') THEN trunc (add_months (current_date(), -1), 'MM')
        ELSE trunc ('${endDateFormatted}', 'MM')
      END
    ) AND (
      CASE
        WHEN date_format ('${endDateFormatted}', 'yyyy-MM') >= date_format (current_date(), 'yyyy-MM') THEN last_day (add_months (current_date, -1))
        ELSE last_day ('${endDateFormatted}')
      END
    )
  GROUP BY 1
  ORDER BY 1
`.trim();
};

/**
 * Gets the users graph data for the last closed month.
 *
 * Retrieves data for the given tenantId and uses endDate to determine the last closed month.
 * Returns data grouped and ordered by date.
 *
 * @param tenantId - The ID of the tenant
 * @param endDate - The end date used to determine the query period
 * @returns SQL query string
 */
export const getUsersGraphInsightsDataQuery = (tenantId: string, endDate: Date) => {
  const endDateFormatted = formatDateForQuery(endDate);
  return `
  SELECT
    Data,
    SUM(UsuariosAtivos) AS UsuariosAtivos,
    SUM(UsuariosEngajados) AS UsuariosEngajados,
    ROUND(
      SUM(UsuariosRecorrentes) / SUM(UsuariosAtivos) * 100,
      2
    ) AS Recorrencia,
    ROUND(
      SUM(UsuariosEngajados) / SUM(UsuariosAtivos) * 100,
      2
    ) AS Engajamento
  FROM
    clients_trustedzone.ilab.gold_smb_overview_data
  WHERE 1 = 1 AND TenantId = '${tenantId}'
    AND date_format (Data, 'yyyy-MM') BETWEEN (
      CASE
        WHEN date_format ('${endDateFormatted}', 'yyyy-MM') >= date_format (current_date(), 'yyyy-MM') THEN trunc (add_months (current_date(), -1), 'MM')
        ELSE trunc ('${endDateFormatted}', 'MM')
      END
    ) AND (
      CASE
        WHEN date_format ('${endDateFormatted}', 'yyyy-MM') >= date_format (current_date(), 'yyyy-MM') THEN last_day (add_months (current_date, -1))
        ELSE last_day ('${endDateFormatted}')
      END
    )
  GROUP BY 1
  ORDER BY 1
  `.trim();
};

export const mapInsightsToDatabricksInsigthsRequestData = (
  insightData: InsightBaseData,
  tenantId: string,
  filter: OverviewFilter,
  insightType: 'msgs' | 'users'
): DatabricksInsightsServiceRequestData => {
  const data = insightData.sort((a: string[], b: string[]) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });

  let dataFront: UsersInsightFrontData | MessagesInsightFrontData;

  switch (insightType) {
  case 'users':
    dataFront = {
      data: data.map((item) => String(item[0])),
      dailyActiveUsers: data.map((item) => Number(item[1])),
      engagedUsersData: data.map((item) => Number(item[2])),
      recurrencyRateData: data.map((item) => Number(item[3])),
      engagementRateData: data.map((item) => Number(item[4])),
    } as UsersInsightFrontData;
    break;

  case 'msgs':
    dataFront = {
      data: data.map((item) => String(item[0])),
      sentMessages: data.map((item) => Number(item[1])),
      receivedMessages: data.map((item) => Number(item[2])),
      activeMessages: data.map((item) => Number(item[3])),
      perUserMessage: data.map((item) => Number(item[4])),
    } as MessagesInsightFrontData;
    break;

  default:
    console.warn('Invalid insight type');
    dataFront = {
      data: data.map((item) => item[0]),
    } as any;
    return {
      inputs: {
        dados_front_segmentacao: mapFilterToInsightsRequestFormat(filter, tenantId),
        dados_front: dataFront,
        tipo_analise: 'msgs',
      },
    };
  }

  const inputs = {
    inputs: {
      dados_front_segmentacao: mapFilterToInsightsRequestFormat(filter, tenantId),
      dados_front: dataFront,
      tipo_analise: insightType,
    },
  };
  return inputs as DatabricksInsightsServiceRequestData;
};

const mapFilterToInsightsRequestFormat = (filter: OverviewFilter, tenantId: string): InsightsSegmentationData => {
  const startDate = filter.startDate ? formatDateForQuery(filter.startDate) : '';
  const endDate = filter.endDate ? formatDateForQuery(filter.endDate) : '';
  const channels = filter.channels?.length > 0 ? filter.channels : [];
  const routerIds = filter.routerIds?.length > 0 ? filter.routerIds : [];
  const botIds = filter.botIds?.length > 0 ? filter.botIds : [];

  return {
    StartDate: startDate,
    EndDate: endDate,
    Channel: channels,
    RouterId: routerIds,
    BotId: botIds,
    TenantId: tenantId,
    Queues: [],
    Category: [],
    TemplateName: [],
    CampaignName: [],
  } as InsightsSegmentationData;
};
