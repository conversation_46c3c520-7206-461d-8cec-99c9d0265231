import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { formatDateForThisChart } from '@utils/date';
import { toBaseFilterWithDateProp } from '@utils/filters';

export interface PercentageAnswerData {
  labels: string[];
  percentages: number[];
}

export class PercentageAnswerService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): PercentageAnswerData {
    if (!response || !response.results) {
      return { labels: [], percentages: [] };
    }

    const dataArray = response?.results || [];
    const labels = dataArray.map((item: string[]) => formatDateForThisChart(item[0]));
    const percentages = dataArray.map((item: string[]) => parseFloat(item[1]) || 0);

    return { labels, percentages };
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface
  ): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('percentage_answer', selectedFilter, tenantId));
  }
}