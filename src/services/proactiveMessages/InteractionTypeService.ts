import { HorizontalBarDataset } from '@components/horizontal_bar_chart';
import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp } from '@utils/filters';
import { formatInteractionTypesChartData } from '@utils/proactiveMessages/HorizontalBarChartHelper';

export class InteractionTypesService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): HorizontalBarDataset[] {
    if (!response || !response.results) {
      return [];
    }

    const atData = response?.results || [];
    return formatInteractionTypesChartData(atData);
  }

  async getData(selectedFilter: ProactiveMessagesFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('interaction_types_chart', selectedFilter, tenantId));
  }
}