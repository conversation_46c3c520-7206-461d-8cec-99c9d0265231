import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { formatPerformanceChartData } from '@utils/proactiveMessages/PerformanceChartHelper';
import { PerformanceChartType } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp } from '@utils/filters';

export class PerformanceChartService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): PerformanceChartType | null {
    if (!response || !response.results) {
      return null;
    }

    const dataArray = response.results || [];
    return dataArray.length > 0 ? formatPerformanceChartData(dataArray) : null;
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface
  ): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('performance_chart', selectedFilter, tenantId));
  }
}