import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp } from '@utils/filters';

export interface MessageModelFailureTableData {
  data: Array<Array<string | number | null>>;
}

export class MessageModelFailureService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): MessageModelFailureTableData | null {
    if (!response || !response.results) {
      return null;
    }

    const dataArray = response.results || [];
    return { data: dataArray };
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface
  ): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('message_model_failure', selectedFilter, tenantId));
  }
}