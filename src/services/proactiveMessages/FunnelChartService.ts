import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { MultipleQueryServiceInterface } from '@services/registry/serviceRegistry';
import { FunnelChartData } from '@typings/FunnelChartData';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp, toProactiveMessagesBaseFilter } from '@utils/filters';
import { formatFunnelChartData } from '@utils/proactiveMessages/FunnelChartHelper';

export class FunnelChartService implements MultipleQueryServiceInterface {
  getMultiFormattedData(responses: BackendResponse[] | undefined): FunnelChartData | undefined {
    if (!responses || responses.length === 0) {
      return undefined;
    }
    const [
      sentMessagesResponse,
      sentIndicatorsResponse,
      sentVariationResponse,
      receivedMessagesResponse,
      receivedIndicatorsResponse,
      receivedVariationResponse,
      readMessagesResponse,
      readIndicatorsResponse,
      readVariationResponse,
      respondedMessagesResponse,
      respondedIndicatorsResponse,
      respondedVariationResponse,
      conversionResponse,
      tooltipsResponse,
    ] = responses;

    return formatFunnelChartData({
      bars: {
        sent: sentMessagesResponse,
        received: receivedMessagesResponse,
        read: readMessagesResponse,
        responded: respondedMessagesResponse,
        conversion: conversionResponse,
        tooltips: tooltipsResponse,
      },
      indicators: {
        sent: {
          status: sentIndicatorsResponse,
          variation: sentVariationResponse,
        },
        received: {
          status: receivedIndicatorsResponse,
          variation: receivedVariationResponse,
        },
        read: {
          status: readIndicatorsResponse,
          variation: readVariationResponse,
        },
        responded: {
          status: respondedIndicatorsResponse,
          variation: respondedVariationResponse,
        },
        conversion: {
          status: conversionResponse,
          variation: '0',
        },
      },
    });
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface
  ): Promise<BackendResponse[]> {
    const queryNames = [
      'funnel_chart_sent_messages',
      'funnel_chart_failure_percentage',
      'funnel_chart_comparative_failure_percentage',
      'funnel_chart_received_messages',
      'funnel_chart_received_percentage',
      'funnel_chart_comparative_received_percentage',
      'funnel_chart_read_messages',
      'funnel_chart_read_percentage',
      'funnel_chart_comparative_read_percentage',
      'funnel_chart_responded_messages',
      'funnel_chart_answered_percentage',
      'funnel_chart_comparative_answered_percentage',
      'funnel_chart_conversion',
      'funnel_chart_tooltips',
    ];
    const queriesToIncludeDateRangeParams = [
      'funnel_chart_comparative_failure_percentage',
      'funnel_chart_comparative_received_percentage',
      'funnel_chart_comparative_read_percentage',
      'funnel_chart_comparative_answered_percentage',
    ];
    const baseFilter = toBaseFilterWithDateProp('', selectedFilter, tenantId);
    const baseFilterWithStartAndEndDateAsParameters = toProactiveMessagesBaseFilter('', selectedFilter, tenantId);
    return await Promise.all(
      queryNames.map((queryName) => {
        const baseFilterToUse = queriesToIncludeDateRangeParams.includes(queryName)
          ? baseFilterWithStartAndEndDateAsParameters
          : baseFilter;
        return blipInsightsService.getData({...baseFilterToUse, query_name: queryName});
      })
    );
  }
}
