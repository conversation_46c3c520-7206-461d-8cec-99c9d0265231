import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp } from '@utils/filters';

export interface TotalUsersTableData {
  data: Array<Array<string | number | null>>;
}

export class UsersResponseService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): TotalUsersTableData | null {
    if (!response || !response.results) {
      return null;
    }
    const dataArray = response.results || [];
    return { data: dataArray };
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface
  ): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('total_users_response', selectedFilter, tenantId));
  }
}