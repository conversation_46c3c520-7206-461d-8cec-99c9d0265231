import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { toBaseFilterWithDateProp } from '@utils/filters';
import { EngagementHeatMapData, formatEngagementHeatMapData } from '@utils/proactiveMessages/EngagementHeatMapHelper';

export class HeatMapService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined): EngagementHeatMapData[] {
    if (!response || !response.results) {
      return [];
    }
    const ehrData = response.results || [];
    return formatEngagementHeatMapData(ehrData);
  }

  async getData(
    selectedFilter: ProactiveMessagesFilter,
    tenantId: string,
    blipInsightsService: BlipInsightsServiceInterface,
  ): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilterWithDateProp('engagement_heatmap_receipt', selectedFilter, tenantId));
  }
}