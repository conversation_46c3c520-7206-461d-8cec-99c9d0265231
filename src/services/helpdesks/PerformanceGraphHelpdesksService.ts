import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import { toHelpdeskBaseFilter } from '@utils/filters';
import { formatPerformanceGraphData } from '@utils/helpdesk/PerformanceGraphHelper';

export class PerformanceGraphHelpdesksService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined)  {
    const data = (response?.results?.filter((i: any) => i !== null)) || [];
    return data.length ? formatPerformanceGraphData(data) : []; // Ensure we return an empty array if no data is present
  }

  async getData(selectedFilter: HelpdeskFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toHelpdeskBaseFilter('performance_graph', selectedFilter, tenantId));
  }
}