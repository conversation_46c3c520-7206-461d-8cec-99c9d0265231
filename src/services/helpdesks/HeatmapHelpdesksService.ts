import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import { toHelpdeskBaseFilterWithDataCriacaoProp } from '@utils/filters';
import { formatTicketsHeatMapData } from '@utils/helpdesk/TicketsHeatMapHelper';

const OPENING_TICKETS_HEATMAP = 'opening_tickets_heatmap';

export class HeatmapHelpdesksService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined)  {
    const othmData = response?.results || [];
    return othmData.length > 0 ? formatTicketsHeatMapData(othmData) : null;
  }

  async getData(selectedFilter: HelpdeskFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toHelpdeskBaseFilterWithDataCriacaoProp(OPENING_TICKETS_HEATMAP, selectedFilter, tenantId));
  }
}