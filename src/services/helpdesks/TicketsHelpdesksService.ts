import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import { toHelpdeskBaseFilter } from '@utils/filters';

const TICKETS_PANEL_QUEUE =  'tickets_panel_queue';

export class TicketsHelpdesksService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined)  {
    return response?.results || [];
  }

  async getData(selectedFilter: HelpdeskFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toHelpdeskBaseFilter(TICKETS_PANEL_QUEUE, selectedFilter, tenantId));
  }
}