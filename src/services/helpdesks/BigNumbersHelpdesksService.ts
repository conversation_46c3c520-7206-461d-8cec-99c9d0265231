import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import { toHelpdeskBaseFilter } from '@utils/filters';

const HELPDESK_BIG_NUMBERS = 'helpdesk_big_numbers';
const HELPDESK_BIG_NUMBERS_PREVIOUS_PERIOD = 'helpdesk_big_numbers_previous_period';

export class BigNumbersHelpdesksService implements QueryServiceInterface {
  constructor(private readonly isPreviousPeriodQuery = false) {
    this.isPreviousPeriodQuery = isPreviousPeriodQuery;
  }

  getFormattedData(response: BackendResponse | undefined)  {
    return (
      (response?.results?.[0]?.filter((item: any) => item !== null)) || []
    );
  }

  async getData(selectedFilter: HelpdeskFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    let filterToUse = selectedFilter;
    if (this.isPreviousPeriodQuery) {
      filterToUse = this.adjustPreviousPeriodDates(selectedFilter);
    }
    const queryName = this.isPreviousPeriodQuery ? HELPDESK_BIG_NUMBERS_PREVIOUS_PERIOD: HELPDESK_BIG_NUMBERS;
    const baseFilter = toHelpdeskBaseFilter(queryName, filterToUse, tenantId, this.isPreviousPeriodQuery)
    return await blipInsightsService.getData(baseFilter);
  }

  private adjustPreviousPeriodDates(selectedFilter: HelpdeskFilter): HelpdeskFilter {
    const { startDate, endDate } = selectedFilter;
    const millisecondsInADay = 1000 * 60 * 60 * 24;
    const millisDifference = endDate.getTime() - startDate.getTime();
    // assure that currentPeriodInDays is at least 1 day
    const currentPeriodInDays = millisDifference ? (millisDifference) / (millisecondsInADay) : 1;
    const selectedFilterCopy = Object.assign({}, selectedFilter);
    selectedFilterCopy.endDate = startDate;
    selectedFilterCopy.startDate = new Date(startDate.getTime() - currentPeriodInDays * millisecondsInADay);
    return selectedFilterCopy;
  }
}