import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { OverviewFilter } from '@typings/OverviewFilter';
import { toBaseFilter } from '@utils/filters';

export class BigNumbersOverviewService implements QueryServiceInterface {
  constructor(private readonly isPreviousPeriodQuery = false) {
    this.isPreviousPeriodQuery = isPreviousPeriodQuery;
  }

  getFormattedData(response: BackendResponse | undefined)  {
    return (
      (response?.results?.[0]?.filter((item: any) => item !== null)) || []
    );
  }

  async getData(selectedFilter: OverviewFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    let filterToUse = selectedFilter;
    if (this.isPreviousPeriodQuery) {
      filterToUse = this.adjustPreviousPeriodDates(selectedFilter);
    }
    const queryName = this.isPreviousPeriodQuery ? 'overview_big_numbers_previous_period' : 'overview_big_numbers';
    return await blipInsightsService.getData(toBaseFilter(queryName, filterToUse, tenantId, this.isPreviousPeriodQuery));
  }

  private adjustPreviousPeriodDates(selectedFilter: OverviewFilter): OverviewFilter {
    const { startDate, endDate } = selectedFilter;
    const millisecondsInADay = 1000 * 60 * 60 * 24;
    const millisDifference = endDate.getTime() - startDate.getTime();
    // assure that currentPeriodInDays is at least 1 day
    const currentPeriodInDays = millisDifference ? (millisDifference) / (millisecondsInADay) : 1;
    const selectedFilterCopy = Object.assign({}, selectedFilter);
    selectedFilterCopy.endDate = startDate;
    selectedFilterCopy.startDate = new Date(startDate.getTime() - currentPeriodInDays * millisecondsInADay);
    return selectedFilterCopy;
  }
}