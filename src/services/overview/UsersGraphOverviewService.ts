import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { OverviewFilter } from '@typings/OverviewFilter';
import { toBaseFilter } from '@utils/filters';
import { formatUsersGraphData } from '@utils/overview/UsersGraphHelper';

export class UsersGraphOverviewService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined) {
    const usersData = response?.results?.filter((i: any) => i !== null) || [];
    return formatUsersGraphData(usersData);
  }

  async getData(selectedFilter: OverviewFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilter('users_graph', selectedFilter, tenantId));
  }
}