import { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { QueryServiceInterface } from '@services/registry/serviceRegistry';
import { OverviewFilter } from '@typings/OverviewFilter';
import { toBaseFilter } from '@utils/filters';
import { formatMessagesGraphData } from '@utils/overview/MessagesGraphHelper';

export class MessagesOverviewService implements QueryServiceInterface {
  getFormattedData(response: BackendResponse | undefined) {
    const messagesData = response?.results?.filter((i: any) => i !== null) || [];
    return formatMessagesGraphData(messagesData);
  }

  async getData(selectedFilter: OverviewFilter, tenantId: string, blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> {
    return await blipInsightsService.getData(toBaseFilter('messages_graph', selectedFilter, tenantId));
  }
}