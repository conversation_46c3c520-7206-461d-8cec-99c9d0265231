import { FeatureToggleClientService } from 'feature-toggle-client';
import { User } from '@typings/User';
import { SentryConfig } from '@utils/monitoring/SentryClient';

export interface Configuration {
  ldclientSdkKey: string;
  ldProxyBaseUrl: string;
  ldProxyEventsUrl: string;
  ldProxyClientStream: string;
  ldStreamReconnectDelay: number;
  blipDomain?: string;
  blipDomainUrl?: string;
  blipWebsocketHostName?: string;
  blipWebsocketHostNameTenant?: string;
  blipWebsocketPort?: string;
  blipWebsocketScheme?: string;
}
// prettier-ignore
export const BLIP_INSIGHTS_FEATURE_TOGGLES ={
  'BLIP_INSIGHTS_VISIBILITY': 'blip-insights-visibility',
  'SENTRY_BLIP_INSIGHTS_CONFIG': 'sentry-blip-insights-config',
  'DATA_MIND_VISIBILITY': 'blip-insights-data-mind-visibility',
};

class FeatureToggleService {
  public async initFeatureToggle(user: User, ldSettings: Configuration) {
    FeatureToggleClientService.getInstance().initializeUser(user, ldSettings.ldclientSdkKey, {
      baseUrl: ldSettings.ldProxyBaseUrl,
      eventsUrl: ldSettings.ldProxyEventsUrl,
      streamUrl: ldSettings.ldProxyClientStream,
      streamReconnectDelay: ldSettings.ldStreamReconnectDelay,
    });

    await FeatureToggleClientService.getInstance().getUserInstance().waitUntilReady();

    return FeatureToggleClientService.getInstance().getUserInstance();
  }

  public async isUserFeatureEnabled(feature: string, defaultValue: boolean): Promise<boolean> {
    return FeatureToggleClientService.getInstance().isUserFeatureEnabled(feature, defaultValue);
  }
  // prettier-ignore
  public async isBlipInsightsEnabled(): Promise<boolean> {
    return FeatureToggleClientService.getInstance().isUserFeatureEnabled(BLIP_INSIGHTS_FEATURE_TOGGLES.BLIP_INSIGHTS_VISIBILITY, false);
  }

  public async getFeatureToggleConfiguration(feature: string): Promise<SentryConfig> {
    return FeatureToggleClientService.getInstance().isUserFeatureEnabled(feature);
  }
}

const featureToggleService = new FeatureToggleService();

export default featureToggleService;
