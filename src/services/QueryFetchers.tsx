import { BaseFilter } from '@src/typings/BaseFilter';
import BlipInsightsService, { BackendResponse, BlipInsightsServiceInterface, DatabricksRequestData } from './BlipInsightsService';

export const getQueryData = (query: string): DatabricksRequestData => {
  return {
    statement: query,
    wait_timeout: '50s',
    warehouse_id: '4b890652affa42e9',
    catalog: 'clients_sandbox',
    schema: 'ilab',
  } as DatabricksRequestData;
};

export const fetchChannelsHelpdesk = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (Canal) from clients_trustedzone.ilab.filter_smb_desk_data where 1 = 1 and tenantid = "${tenantId}" and Canal IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchRoutersHelpdesk = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (routerid) from clients_trustedzone.ilab.filter_smb_desk_data where 1 = 1 and tenantid = "${tenantId}" and routerid IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchBotsHelpdesk = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (botid) from clients_trustedzone.ilab.filter_smb_desk_data where 1 = 1 and tenantid = "${tenantId}" and botid IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchQueues = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (fila) from clients_trustedzone.ilab.filter_smb_desk_data where 1 = 1 and tenantid = "${tenantId}" and fila IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchChannelsOverview = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (Canal) from clients_trustedzone.ilab.filter_smb_overview_data where 1 = 1 and tenantid = "${tenantId}" and Canal IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchRoutersOverview = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (RouterBotId) from clients_trustedzone.ilab.filter_smb_overview_data where 1 = 1 and tenantid = "${tenantId}" and RouterBotId IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchBotsOverview = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (BotId) from clients_trustedzone.ilab.filter_smb_overview_data where 1 = 1 and tenantid = "${tenantId}" and BotId IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchRoutersProactiveMessages = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (RouterBotId) from rdproduct_trustedzone.ilab.filter_smb_notifications_data where 1 = 1 and tenantid = "${tenantId}" and RouterBotId IS NOT NULL LIMIT 1000`;
  return await blipInsightsService.getData(getQueryData(query));
};
export const fetchBotsProactiveMessages = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (BotId) from rdproduct_trustedzone.ilab.filter_smb_notifications_data where 1 = 1 and tenantid = "${tenantId}" and BotId IS NOT NULL LIMIT 1000`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchCategory = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (Category) from rdproduct_trustedzone.ilab.filter_smb_notifications_data where 1 = 1 and tenantid = "${tenantId}" and Category IS NOT NULL LIMIT 1000`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchTemplate = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (Template) from rdproduct_trustedzone.ilab.filter_smb_notifications_data where 1 = 1 and tenantid = "${tenantId}" and Template IS NOT NULL`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchCampaign = async (
  blipInsightsService: BlipInsightsService,
  tenantId: string
): Promise<BackendResponse> => {
  const query = `select distinct (Campaign) from rdproduct_trustedzone.ilab.filter_smb_notifications_data where 1 = 1 and tenantid = "${tenantId}" and Campaign IS NOT NULL LIMIT 1000`;
  return await blipInsightsService.getData(getQueryData(query));
};

export const fetchLastUpdate = async (blipInsightsService: BlipInsightsServiceInterface): Promise<BackendResponse> => {
  return await blipInsightsService.getData({query_name: 'last_update'} as BaseFilter);
};

const lastUpdateQuery = `
  WITH historico AS (
    DESCRIBE HISTORY clients_trustedzone.ilab.silver_smb_desk_main_data
  )
  SELECT 
    concat( 
      date_format(from_utc_timestamp(max(timestamp), 'America/Sao_Paulo'), 'dd/MM/yyyy'), 
      ' às ', 
      date_format(from_utc_timestamp(max(timestamp), 'America/Sao_Paulo'), 'HH'), 
      'h',
      date_format(from_utc_timestamp(max(timestamp), 'America/Sao_Paulo'), 'mm')
    ) AS timestamp_formatado
  FROM historico
  WHERE operation IN ('MERGE', 'WRITE');`;

export const getUsersForTenant = (tenantId: string) => `
WITH ranked_data AS (
  SELECT
    EmailsAcesso,
    ROW_NUMBER() OVER (PARTITION BY tenantid ORDER BY updated_at DESC) AS rn
  FROM clients_trustedzone.ilab.filter_smb_overview_data
  WHERE tenantid = '${tenantId}'
)
SELECT EmailsAcesso
FROM ranked_data
WHERE rn = 1`;