import { BaseFilter } from '@src/typings/BaseFilter';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';

export interface BackendResponse {
  manifest?: any;
  result?: any;
  statement_id?: string;
  status?: {
    state: string;
  };
  results: any[];
}

export interface BdiApiResponse {
  results: any;
}

export interface InsightsSegmentationData {
  TenantId: string;
  StartDate: string;
  EndDate: string;
  Channel: string[];
  RouterId: string[];
  BotId: string[];
  Queues: string[];
  Category: string[];
  TemplateName: string[];
  CampaignName: string[];
}

export interface DatabricksInsightsServiceRequestData {
  inputs: {
    dados_front_segmentacao: InsightsSegmentationData;
    dados_front: unknown;
    tipo_analise: 'desk' | 'users' | 'msgs' | 'notifications';
  };
}

export interface FrontData {
  data: string[];
  tickets_abertos: number[];
  tickets_fechados: number[];
  tempo_medio_atendimento: string[];
  tempo_medio_espera: string[];
  tempo_medio_resposta_atendente: string[];
  tempo_medio_fila: string[];
  tempo_medio_primeira_resposta_atendente: string[];
}

export interface DatabricksRequestData {
  statement: string;
  wait_timeout: string;
  warehouse_id: string;
  catalog: string;
  schema: string;
}

export interface InsightsResponse {
  predictions: {
    analise_llm: string;
    contexto: string;
    dados_front: string;
    dados_front_segmentacao: string;
    insight_llm: string;
    tipo_analise: string;
  };
  results?: any[];
}

export interface BlipInsightsServiceInterface {
  getInsightsData(
    data: DatabricksInsightsServiceRequestData | BaseFilter,
    endpoint?: string
  ): Promise<InsightsResponse>;

  getData(
    data: DatabricksRequestData | DatabricksInsightsServiceRequestData | BaseFilter,
    endpoint?: string,
    retryCount?: number
  ): Promise<BackendResponse>;
}

export class BlipInsightsService implements BlipInsightsServiceInterface {
  protected backendUrlStatements = 'https://apim-bots-hmg-001.azure-api.net/blip-insights';
  protected backendUrlStatementsHmg = 'https://apim-bots-hmg-001.azure-api.net/blip-insights-hmg';
  protected backendUrlInsights = 'https://apim-bots-hmg-001.azure-api.net/blip-insights-invocations';
  protected backendUrlInsightsHmg = 'https://apim-bots-hmg-001.azure-api.net/blip-insights-invocations-hmg';
  protected maxRetryCount = 3;
  protected BLIP_ACCOUNT_ISSUER = 'account.blip.ai';
  protected BLIP_AZURE_ISSUER = 'azure.blip.ai';
  protected DEFAULT_INSIGHTS_API_PATH_HMG = '/serving-endpoints/insight_agent_pocblipdatainsights/invocations';
  protected DEFAULT_INSIGHTS_API_PATH = '/serving-endpoints/insight_agent_blipdatainsights/invocations';
  protected DEFAULT_STATEMENTS_API_PATH = '/api/2.0/sql/statements';
  protected issuer: string;

  constructor(protected authorization: string, protected readonly settings: any) {
    const url = window.location.href;
    if (url.includes('hmg.blip.ai') || url.includes('localhost')) {
      this.backendUrlStatements = this.backendUrlStatementsHmg;
      this.backendUrlInsights = this.backendUrlInsightsHmg;
      this.DEFAULT_INSIGHTS_API_PATH = this.DEFAULT_INSIGHTS_API_PATH_HMG;
    }
    this.authorization = authorization;
    this.issuer = this.BLIP_AZURE_ISSUER; // set default issuer
    this.setIssuer();
  }

  protected setIssuer() {
    try {
      const oidcData = localStorage.getItem('oidc.azureB2CData');
      const isEnabledBlipLogin = oidcData ? JSON.parse(oidcData).isEnabled : false;
      this.issuer = isEnabledBlipLogin ? this.BLIP_AZURE_ISSUER : this.BLIP_ACCOUNT_ISSUER;
    } catch (error) {
      console.error('Error parsing oidc.azureB2CData from localStorage:', error);
    }
  }

  async getInsightsData(
    data: DatabricksInsightsServiceRequestData | BaseFilter,
    endpoint = this.DEFAULT_INSIGHTS_API_PATH
  ): Promise<InsightsResponse> {
    const response: Response = await this.executeRequest(data, endpoint, this.backendUrlInsights);
    return await response.json();
  }

  async getData(
    data: DatabricksRequestData | DatabricksInsightsServiceRequestData | BaseFilter,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH,
    retryCount = 0
  ): Promise<BackendResponse> {
    const response: Response = await this.executeRequest(data, endpoint, this.backendUrlStatements);
    const responseData: BackendResponse = await response.json();

    if (responseData?.status?.state?.toString().toUpperCase() === 'PENDING') {
      return await this.handlePendingRequest(data, responseData, endpoint, retryCount);
    }

    if (responseData?.status?.state?.toString().toUpperCase() !== 'SUCCEEDED') {
      throw new Error('Failed to get data from backend: API returned Failure');
    }

    return responseData as BackendResponse;
  }

  protected handlePendingRequest(
    data: DatabricksRequestData | DatabricksInsightsServiceRequestData | BaseFilter,
    responseData: any,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH,
    retryCount: number
  ): Promise<any> {
    console.warn('pending status returned for blip insights, retrying request...');
    if (retryCount >= this.maxRetryCount) {
      throw new Error('Max retry count reached for blip insights data');
    }

    retryCount++;

    return new Promise((resolve) => {
      setTimeout(async () => resolve(await this.getData(data, endpoint, retryCount)), 1000);
    });
  }

  protected async executeRequest(
    data: DatabricksRequestData | DatabricksInsightsServiceRequestData | BaseFilter,
    endpoint = this.DEFAULT_STATEMENTS_API_PATH,
    backendUrl = this.backendUrlStatements
  ): Promise<Response> {
    const url = `${backendUrl}${endpoint}`;
    return await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ' + this.authorization,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': '*',
        'Content-Type': 'application/json',
        'Http-Session-Authentication-External-Issuer': this.issuer,
      },
      body: JSON.stringify(data),
    });
  }
}

export default BlipInsightsService;
