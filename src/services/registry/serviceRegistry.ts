import { AllChartsDataInterface } from '@pages/templatesComparing';
import BlipInsightsService, { BackendResponse, BlipInsightsServiceInterface } from '@services/BlipInsightsService';

export interface QueryServiceInterface {
  getData: (selectedFilter: any, tenantId: string, blipInsightsService: BlipInsightsServiceInterface, vision?: string) => Promise<any>;
  getFormattedData: (response: BackendResponse | undefined) => any;
}

export interface MultipleQueryServiceInterface {
  getData: (selectedFilter: any, tenantId: string, blipInsightsService: BlipInsightsServiceInterface) => Promise<any>;
  getMultiFormattedData: (response: BackendResponse[] | undefined) => any;
}

interface ServiceRegistryInterface {
  [key: string]: QueryServiceInterface;
}

interface MultiServiceRegistryInterface {
  [key: string]: MultipleQueryServiceInterface;
}

export class ServiceRegistry {
  private serviceRegistry: ServiceRegistryInterface = {};
  private multiServiceRegistry: MultiServiceRegistryInterface = {};
  private blipInsightsService: BlipInsightsService;
  private tenantId: string;

  constructor(blipInsightsService: BlipInsightsService, tenantId: string) {
    this.blipInsightsService = blipInsightsService;
    this.tenantId = tenantId;
  }

  addServiceToRegistry(serviceName: string, service: QueryServiceInterface) {
    if (Object.hasOwn(this.serviceRegistry, serviceName)) {
      return;
    }
    this.serviceRegistry[serviceName] = service;
  }

  addMultiServiceToRegistry(serviceName: string, service: MultipleQueryServiceInterface) {
    if (Object.hasOwn(this.multiServiceRegistry, serviceName)) {
      return;
    }
    this.multiServiceRegistry[serviceName] = service;
  }

  clearRegistry() {
    this.serviceRegistry = {};
  }

  async loadAllDataFromRegisteredServices(selectedFilter: any): Promise<AllChartsDataInterface> {
    // build array with keys of the serviceRegistry
    const serviceResponseKeys = [...Object.keys(this.serviceRegistry), ...Object.keys(this.multiServiceRegistry)].map(
      (key) => `${key}Response`
    );
    const promises = Array.from(
      Object.values(this.serviceRegistry).map((service) =>
        service.getData(selectedFilter, this.tenantId, this.blipInsightsService)
      )
    );
    const multiPromises = Array.from(
      Object.values(this.multiServiceRegistry).map((service) =>
        service.getData(selectedFilter, this.tenantId, this.blipInsightsService)
      )
    );
    const results = await Promise.all([...promises, ...multiPromises]);
    const response: Record<string, any> = {};
    // build an object with the keys and the results
    for (let i = 0; i < serviceResponseKeys.length; i++) {
      const key = serviceResponseKeys[i];
      response[key] = results[i];
    }
    return response as AllChartsDataInterface;
  }

  getService(serviceName: string) {
    if (!Object.hasOwn(this.serviceRegistry, serviceName)) {
      return null;
    }
    return this.serviceRegistry[serviceName];
  }

  getMultiService(serviceName: string) {
    if (!Object.hasOwn(this.multiServiceRegistry, serviceName)) {
      return null;
    }
    return this.multiServiceRegistry[serviceName];
  }
}