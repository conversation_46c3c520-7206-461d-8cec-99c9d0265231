import '@testing-library/jest-dom';

// Mock WebSocket to prevent connection errors
global.WebSocket = class WebSocket {
  constructor() {
    this.readyState = 1; // OPEN
  }

  send() {}
  close() {}
  addEventListener() {}
  removeEventListener() {}
};

// Mock the entire blip-services package
jest.mock('blip-services', () => ({
  BlipService: class MockBlipService {
    constructor(config) {
      this.config = config;
      this.connected = false;
    }

    async connect() {
      this.connected = true;
      return Promise.resolve();
    }

    async disconnect() {
      this.connected = false;
      return Promise.resolve();
    }

    async processCommand(command, timeout) {
      return Promise.resolve({
        id: command.id,
        method: command.method,
        status: 'success',
        resource: { items: [] },
      });
    }

    isConnected() {
      return this.connected;
    }
  },
}));

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};
