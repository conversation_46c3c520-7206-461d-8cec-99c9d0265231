import React, { createContext, useContext } from 'react';
import { BlipInsightsService, BlipInsightsServiceInterface } from '@services/BlipInsightsService';
import { User } from '@typings/User';
import { getQueryData, getUsersForTenant } from '@services/QueryFetchers';
import { useAppContext } from '@contexts/AppContext';
import { APIBlipInsightsService } from '@services/APIBlipInsightsServices';
import { BaseFilter } from '@src/typings/BaseFilter';

interface BlipInsightsServiceContext {
  blipInsightsService: BlipInsightsService;
  apiBlipInsightsService: APIBlipInsightsService;
}

// Create a new context for the BlipInsightsServices
const BlipInsightsServicesContext = createContext<BlipInsightsServiceContext | null>(null);

interface BlipServicesProviderProps {
  authtoken: string;
  settings: string;
  children: React.ReactNode;
}

// Custom hook to access the BlipInsightsServices from the context
export function useBlipInsightsServices(): BlipInsightsServiceContext {
  const blipInsightsServices = useContext(BlipInsightsServicesContext);
  if (!blipInsightsServices) {
    throw new Error('useBlipInsightsServices must be used within a BlipInsightsServicesProvider');
  }
  return blipInsightsServices;
}

let oneRequestIsRunning = false; // Flag to prevent multiple requests

const checkUserAccess = async (
  blipInsightsService: BlipInsightsServiceInterface, 
  user: User, tenantId: string,
  isCheckingUserAcess: boolean,
  setIsCheckingUserAcess: (checking: boolean) => void,
  setUserHasAccess: (hasAccess: boolean) => void,
) => {
  if (!isCheckingUserAcess || oneRequestIsRunning) {	
    return;
  }

  try{
    oneRequestIsRunning = true;
    const baseFilter = {
      query_name: 'user_validation',
      parameters: {
        tenant_id: tenantId
      }
    } as BaseFilter;
    const response = await blipInsightsService.getData(baseFilter);
    const allUsersForTenant = response?.results?.length && response?.results[0][0]?.split(';') || [];
    const userHasAccess = allUsersForTenant.length && allUsersForTenant.includes(user?.email);
    setUserHasAccess(userHasAccess);
  } catch (error) {
    console.error(error);
    setUserHasAccess(false);
  } finally {
    oneRequestIsRunning = false;
    setIsCheckingUserAcess(false);
  }
};
// Create the BlipInsightsServicesProvider component
export function BlipInsightsServicesProvider({ authtoken, settings, children }: BlipServicesProviderProps) {
  const blipInsightsService = new BlipInsightsService(authtoken, settings);
  const apiBlipInsightsService = new APIBlipInsightsService(authtoken, settings);
  const services = {blipInsightsService, apiBlipInsightsService};
  const { user, tenantId, isCheckingUserAcess, setIsCheckingUserAcess, setUserHasAccess } = useAppContext();
  checkUserAccess(apiBlipInsightsService, user, tenantId, isCheckingUserAcess, setIsCheckingUserAcess, setUserHasAccess);
  return (
    <BlipInsightsServicesContext.Provider value={services}>{children}</BlipInsightsServicesContext.Provider>
  );
}
