import React, { createContext } from 'react';

import LocalizedStrings, { LocalizedStringsMethods } from 'react-localization';
import { TranslationContextData } from '@typings/Configuration';

interface TranslationProviderProps {
  language: 'pt' | 'en' | 'es';
  children: React.ReactNode;
}

export const TranslationContext = createContext<TranslationContextData>({} as TranslationContextData);

export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children, language }) => {
  const instance = new LocalizedStrings({ en: {}, pt: {}, es: {} });

  const translate = <T, K extends keyof T>(localization: T) => {
    instance.setContent(localization);
    setLanguage(language);

    return instance as LocalizedStringsMethods & T[K];
  };

  const setLanguage = (selectedLanguage: 'pt' | 'en' | 'es') => {
    instance.setLanguage(selectedLanguage);
  };

  return (
    <TranslationContext.Provider
      value={{
        translate,
        setLanguage,
      }}
    >
      {children}
    </TranslationContext.Provider>
  );
};

export function useTranslation(): TranslationContextData {
  const context = React.useContext(TranslationContext);

  if (!context) {
    throw new Error('use translation must be used within a TranslationProvider');
  }

  return context;
}
