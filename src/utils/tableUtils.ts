import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { parseTimeToSeconds } from './time';

export const processTableData = (
  data: Array<Array<string | number | null>>,
  columnTypes: Array<string>
): Array<Array<string | number | null>> => {
  if (!data) return [];

  return data
    .filter((row) => {
      return !row.some((cell, index) => columnTypes[index] === 'string' && cell == null);
    })
    .map((row) =>
      row.map((cellFromApi, index) => {
        const cell = cellFromApi ? cellFromApi.toString().trim() : '';
        const columnType = columnTypes[index];
    
        if (columnType === 'string' && index === 0 && typeof cell === 'string') {
          if (cell.trim().startsWith('{') && cell.trim().endsWith('}')) {
            try {
              const cleanedCell = cell.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
              const parsed = JSON.parse(cleanedCell);
              if (typeof parsed === 'object' && parsed !== null) {
                return 'Texto sem formatação adequada';
              }
            } catch (e) {
              console.warn('Error parsing JSON:', e);
            }            
          }
        }

        const isEmpty = cell == null || cell === '';

        if (!isEmpty) {
          if (columnType === 'percentage') {
            const trimmed = cell.toString().trim();
            return trimmed.endsWith('%') ? trimmed : `${parseFloat(trimmed).toFixed(1)}%`;
          }
          return columnType === 'number' ? formatNumberForChart(parseFloat(cell)) : cell;
        }

        if (!isEmpty) return cell;
      
        switch (columnType) {
        case 'percentage':
          return '0.0%';
        case 'time':
          return '00:00:00';
        case 'number':
          return 0;
        default:
          return cellFromApi;
        }
      })
    );
};

export const parseTableValue = (value: any, type: string): any => {
  switch (type) {
  case 'number':
    return value != null ? Number(String(value)) : null;
  case 'percentage':
    return typeof value === 'string'
      ? Number(value.replace('%', '').trim())
      : null;
  case 'time':
    return typeof value === 'string' ? parseTimeToSeconds(value) : null;
  default:
    return value ?? null;
  }
};

export const sortTableData = (
  data: Array<Array<string | number | null>>,
  columnTypes: Array<string>,
  sortedColumn: number,
  sortDirection: number
): Array<Array<string | number | null>> => {
  const columnType = columnTypes[sortedColumn];
  return [...data].sort((a, b) => {
    const valA = parseTableValue(a[sortedColumn], columnType);
    const valB = parseTableValue(b[sortedColumn], columnType);

    const isNullA = valA == null;
    const isNullB = valB == null;

    if (isNullA && !isNullB) return 1;
    if (!isNullA && isNullB) return -1;
    if (isNullA && isNullB) return 0;

    if (valA < valB) return -sortDirection;
    if (valA > valB) return sortDirection;
    return 0;
  });
};