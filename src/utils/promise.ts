export interface PromiseContainer<T> {
  resolve: () => void;
  reject: () => void;
  promise: Promise<unknown>;
  result: T;
}

export const executePromiseCachedCallback = async <T>(
  promiseContainer: PromiseContainer<T>,
  callback: () => Promise<T>
): Promise<T> => {
  if (promiseContainer.promise === undefined) {
    const tempPromiseContainer = createPromiseContainer();
    promiseContainer.promise = tempPromiseContainer.promise;
    promiseContainer.resolve = tempPromiseContainer.resolve;
    promiseContainer.reject = tempPromiseContainer.reject;

    try {
      const result = await callback();
      promiseContainer.result = result;
      promiseContainer.resolve();
    } catch (error) {
      promiseContainer.reject();
      throw error;
    }

    return promiseContainer.result;
  } else {
    await promiseContainer.promise;

    return promiseContainer.result;
  }
};

export const createPromiseContainer = <T>(): PromiseContainer<T> => {
  const promiseContainer = {} as PromiseContainer<T>;
  const promise = new Promise<void>((resolve, reject) => {
    promiseContainer.resolve = resolve;
    promiseContainer.reject = reject;
  });
  promiseContainer.promise = promise;

  return promiseContainer;
};
