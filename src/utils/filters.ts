import { BaseFilter } from '@src/typings/BaseFilter';
import { OverviewFilter } from '@src/typings/OverviewFilter';
import { formatDateForQuery } from './time';
import { HelpdeskFilter } from '@src/typings/HelpdeskFilter';
import { ProactiveMessagesFilter } from '@src/typings/ProactiveMessagesFilter';

export const toBaseFilter = (queryName:string, selectedFilter: OverviewFilter | HelpdeskFilter | ProactiveMessagesFilter, tenantId: string, isPreviousPeriod = false): BaseFilter => {
  const filter = {
    query_name: queryName,
    parameters: {
      tenant_id: tenantId,
      ...(isPreviousPeriod && {
        previous_start_date: formatDateForQuery(selectedFilter.startDate),
        previous_end_date: formatDateForQuery(selectedFilter.endDate),
      }),
    },
    filters: {
      ...(!isPreviousPeriod && selectedFilter.startDate && selectedFilter.endDate && {
        data: {
          start_date: formatDateForQuery(selectedFilter.startDate),
          end_date: formatDateForQuery(selectedFilter.endDate),
        },
      }),
      ...('channels' in selectedFilter && selectedFilter.channels && selectedFilter.channels.length > 0) && {
        Canal: selectedFilter.channels,
      },
      ...('routerIds' in selectedFilter && selectedFilter.routerIds && selectedFilter.routerIds.length > 0) && {
        RouterBotId: selectedFilter.routerIds,
      },
      ...('botIds' in selectedFilter && selectedFilter.botIds && selectedFilter.botIds.length > 0) && {
        BotId: selectedFilter.botIds,
      },
      ...('queues' in selectedFilter && selectedFilter.queues && selectedFilter.queues.length > 0) && {
        Fila: selectedFilter.queues,
      },
      ...('category' in selectedFilter && selectedFilter.category && selectedFilter.category.length > 0) && {
        Category: selectedFilter.category,
      },
      ...('template' in selectedFilter && selectedFilter.template && selectedFilter.template.length > 0) && {
        Template: selectedFilter.template,
      },
      ...('campaign' in selectedFilter && selectedFilter.campaign && selectedFilter.campaign.length > 0) && {
        Campaign: selectedFilter.campaign,
      },
    }
  };
  return filter as BaseFilter;
}

export const toHelpdeskBaseFilter = (queryName:string, selectedFilter: HelpdeskFilter, tenantId: string, isPreviousPeriod = false): BaseFilter => {
  const baseFilter = toBaseFilter(queryName, selectedFilter, tenantId, isPreviousPeriod);
  if (baseFilter.filters?.RouterBotId) {
    baseFilter.filters.RouterId = baseFilter.filters.RouterBotId;
    delete baseFilter.filters.RouterBotId;
  }
  return baseFilter;
}

export const toHelpdeskBaseFilterWithDataFechamentoProp = (queryName:string, selectedFilter: HelpdeskFilter, tenantId: string, isPreviousPeriod = false): BaseFilter => {
  const baseFilter = toHelpdeskBaseFilter(queryName, selectedFilter, tenantId, isPreviousPeriod);
  if (baseFilter.filters.data) {
    baseFilter.filters.data_fechamento = { ...baseFilter.filters.data };
    delete baseFilter.filters.data;
  }
  return baseFilter;
}

export const toHelpdeskBaseFilterWithDataCriacaoProp = (queryName:string, selectedFilter: HelpdeskFilter, tenantId: string, isPreviousPeriod = false): BaseFilter => {
  const baseFilter = toHelpdeskBaseFilter(queryName, selectedFilter, tenantId, isPreviousPeriod);
  if (baseFilter.filters.data) {
    baseFilter.filters.data_criacao = { ...baseFilter.filters.data };
    delete baseFilter.filters.data;
  }
  return baseFilter;
}

export const toProactiveMessagesBaseFilter = (queryName:string, selectedFilter: ProactiveMessagesFilter, tenantId: string): BaseFilter => {
  const filter = toBaseFilter(queryName, selectedFilter, tenantId);
  if (selectedFilter.endDate) {
    filter.parameters = { ...filter.parameters, end_date: formatDateForQuery(selectedFilter.endDate) };
  }
  if (selectedFilter.startDate) {
    filter.parameters = { ...filter.parameters, start_date: formatDateForQuery(selectedFilter.startDate) };
    delete filter.filters.data;
  }
  delete filter.filters.RouterBotId;
  delete filter.filters.BotId;
  delete filter.filters.Campaign;
  delete filter.filters.Template;
  delete filter.filters.Category;
  return filter as BaseFilter;
}

export const toBaseFilterWithDateProp = (queryName:string, selectedFilter: ProactiveMessagesFilter, tenantId: string): BaseFilter => {
  const filter = toBaseFilter(queryName, selectedFilter, tenantId);
  if (filter.filters.data) {
    filter.filters.Date = { ...filter.filters.data };
    delete filter.filters.data;
  }
  return filter as BaseFilter;
}

export const toInsightsFilter = (queryName:string, selectedFilter: OverviewFilter | HelpdeskFilter | ProactiveMessagesFilter, tenantId: string): BaseFilter => {
  const filter = toBaseFilter(queryName, selectedFilter, tenantId);
  if (filter.parameters) {
    filter.parameters.end_date = formatDateForQuery(selectedFilter.endDate);
  }
  delete filter.filters.data;
  return filter as BaseFilter;
}
