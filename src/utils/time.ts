export const parseTimeToSeconds = (time: string) => {
  const timeRegex = /^(\d{1,5}d) ([0-5]\d):([0-5]\d)$/;
  if (timeRegex.test(time)) {
    return parseTimeIntervalWithDaysToSeconds(time);
  }

  const parts = time.split(':');
  if (parts.length !== 3) return 0; // Invalid time format

  const hours = parseInt(parts[0]);
  const minutes = parseInt(parts[1]);
  const seconds = parseInt(parts[2]);

  return hours * 3600 + minutes * 60 + seconds;
};

export const parseTimeIntervalWithDaysToSeconds = (value: string) => {
  const timeWithDaysRegex = /^(\d{1,5}d) ([0-5]\d):([0-5]\d)$/;
  if (!timeWithDaysRegex.test(value)) return 0; // Invalid format

  const [days, hours, minutes] = value.split(/d |:/).map(Number);
  return days * 24 * 3600 + hours * 3600 + minutes * 60;
};

export const formatDateForQuery = (date: Date): string => {
  // format to yyyy-mm-dd
  const year = date.getFullYear();
  let month = String(date.getMonth() + 1).padStart(2, '0');
  let day = String(date.getDate()).padStart(2, '0');

  if (month.length < 2) {
    month = '0' + month;
  }

  if (day.length < 2) {
    day = '0' + day;
  }

  return `${year}-${month}-${day}`;
};
