import { parseTimeToSeconds } from '@utils/time';
import { TooltipPostionType } from 'blip-ds/dist/types/components/tooltip/tooltip';
import { formatNumberForChart } from './PerformanceGraphHelper';

export interface BigNumberCardData {
  type: string; // "period"
  title: string; // "Tickets abertos"
  variation: string; //"+12"
  icon: string; //"ticket"
  tooltip: TooltipData; //{{ text: 'Total de tickets abertos no período', position: 'top-left' }}
  value: string; //"12 Mil"
  chipColor: 'disabled' | 'success' | 'danger' | 'default' | 'outline' | 'info' | 'warning' | undefined; //"disabled"
}

interface TooltipData {
  text: string; // ex "Total de tickets abertos no período"
  position: TooltipPostionType; // ex "top-left"
}

const NA_STRING = 'N/A'; // String to represent Not Available

const titles = [
  'Tickets abertos',
  'Tickets fechados',
  'Tickets perdidos',
  'Tickets abandonados',
  'Tempo médio de espera',
  'Tempo médio de fila',
  'Tempo médio de atendimento',
  'Tempo médio de resposta',
];

const tooltips = [
  {
    text: 'Total de tickets abertos no período',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Total de atendimentos perdidos, abandonados ou concluídos no período',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Atendimentos encerrados por clientes em espera, antes de serem atribuídos a um atendente',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Atendimentos encerrados por clientes depois de serem atribuídos a um atendente',
    position: 'bottom-center' as TooltipPostionType,
  },
  {
    text: 'Tempo médio para um cliente receber o primeiro atendimento depois que é atribuído a uma fila',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Tempo médio que os clientes aguardam na fila até serem atribuídos a um atendente',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Tempo médio de duração dos atendimentos, a partir da primeira mensagem do atendente até a finalização do atendimento',
    position: 'top-left' as TooltipPostionType,
  },
  {
    text: 'Tempo médio para os atendentes responderem às mensagens do cliente',
    position: 'left-bottom' as TooltipPostionType,
  },
];

const icons = ['ticket', 'ticket', 'ticket', 'ticket', 'clock', 'clock', 'clock', 'clock'];

export const formatBigNumbersData = (
  bigNumberCurrentPeriod: (string|number)[],
  bigNumberPreviousPeriod: (string|number)[]
): BigNumberCardData[] => {
  const bigNumberData: BigNumberCardData[] = [];
  for (let i = 0; i < bigNumberCurrentPeriod.length; i++) {
    const currentPeriod = bigNumberCurrentPeriod[i].toString();
    const previousPeriod = bigNumberPreviousPeriod[i].toString();

    const variation = calculateVariation(currentPeriod, previousPeriod);

    const formattedData: BigNumberCardData = {
      type: 'period',
      title: titles[i],
      variation: `${variation}${variation === NA_STRING ? '' : '%'}`,
      icon: icons[i],
      tooltip: tooltips[i],
      value: formatValue(currentPeriod),
      chipColor: getChipColor(variation),
    };

    bigNumberData.push(formattedData);
  }
  return bigNumberData;
};

const getChipColor = (variation: string) => {
  if (!variation) return 'disabled';
  if (variation.trim().startsWith('-')) return 'danger';
  const varationInNumber = parseInt(variation.replace(/\D/g, ''));
  if (variation === NA_STRING || varationInNumber === 0) return 'disabled';
  return 'success';
};

const formatValue = (value: string) => {
  if (isNaN(+value)) return value;

  return formatNumberForChart(parseInt(value));
};

const calculateVariation = (currentPeriod: string | number, previousPeriod: string | number) => {
  if (!currentPeriod || !previousPeriod) return NA_STRING;
  if (typeof currentPeriod === 'number') {
    currentPeriod = currentPeriod.toString();
  }
  if (typeof previousPeriod === 'number') {
    previousPeriod = previousPeriod.toString();
  }
  // if is time format like 00:00:00
  if (currentPeriod.includes(':') && previousPeriod.includes(':')) {
    return calculateTimeVariation(currentPeriod, previousPeriod);
  }
  return calculateNumberVariation(currentPeriod, previousPeriod);
};

const calculateTimeVariation = (currentPeriod: string, previousPeriod: string) => {
  const currentPeriodValue = parseTimeToSeconds(currentPeriod);
  const previousPeriodValue = parseTimeToSeconds(previousPeriod);

  return calculateNumberVariation(currentPeriodValue.toString(), previousPeriodValue.toString());
};

const calculateNumberVariation = (currentPeriod: string, previousPeriod: string) => {
  const currentPeriodValue = parseFloat(currentPeriod);
  const previousPeriodValue = parseFloat(previousPeriod);

  if (previousPeriodValue === 0) {
    return NA_STRING; // Avoid division by zero
  }

  const variation = ((currentPeriodValue - previousPeriodValue) / previousPeriodValue) * 100;
  return `${variation.toFixed(0)}`;
};
