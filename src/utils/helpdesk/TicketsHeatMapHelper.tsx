import { capitalizeFirstLetter } from '@utils/text';

export interface TicketsHeatMapData {
  interval: number;
  ticketsData: number[][];
  lineLabels: string[]
}

export const formatTicketsHeatMapData = (data: string[][]) => {
  const filteredData = data.filter((dayData: string[]) => dayData[0] !== null);
  const lineLabels: string[] = [];
  const parsedData = filteredData.map((weekday: string[]) => {
    const weekdayName = weekday.shift() as string; // primeiro valor é o nome do dia ex.: domingo
    lineLabels.push(capitalizeFirstLetter(weekdayName));
    return weekday.map(valueHour => parseFloat(valueHour));
  });
  const min = Math.min(...parsedData.map((weekday: number[]) => Math.min(...weekday)));
  const max = Math.max(...parsedData.map((weekday: number[]) => Math.max(...weekday)));
  return {
    ticketsData: parsedData,
    interval: max - min,
    lineLabels
  };
};
