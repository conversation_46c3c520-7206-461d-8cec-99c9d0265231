export interface PerformanceGraphData {
  labelPeriods: string[];
  selectOptions: {
    TMA: string;
    TME: string;
    TMF: string;
    TMR: string;
  };
  openTicketsData: number[];
  closedTicketsData: number[];
  avarageTime: {
    TMA: number[];
    TME: number[];
    TMF: number[];
    TMR: number[];
  };
}

const getPerformanceGraphDataModel = (): PerformanceGraphData => {
  return {
    labelPeriods: [],
    selectOptions: {
      TMA: 'Tempo médio de atendimento',
      TME: 'Tempo médio de espera',
      TMF: 'Tempo médio de fila',
      TMR: 'Tempo médio de resposta',
    },
    openTicketsData: [],
    closedTicketsData: [],
    avarageTime: {
      TMA: [],
      TME: [],
      TMF: [],
      TMR: [],
    },
  };
};
export const formatPerformanceGraphData = (data: string[]) => {
  data.sort((a, b) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });
  const response = getPerformanceGraphDataModel();
  data.map((item) => {
    const labelPeriod = formatDateForThisChart(item[0]);
    const openTickets = parseInt(item[1]);
    const closedTickets = parseInt(item[2]);
    const tma = parseTimeToMinutes(item[3]);
    const tme = parseTimeToMinutes(item[4]);
    const tmr = parseTimeToMinutes(item[5]);
    const tmf = parseTimeToMinutes(item[6]);

    response.labelPeriods.push(labelPeriod);
    response.openTicketsData.push(openTickets);
    response.closedTicketsData.push(closedTickets);
    response.avarageTime.TMA.push(tma ? tma : 0);
    response.avarageTime.TME.push(tme ? tme : 0);
    response.avarageTime.TMR.push(tmr ? tmr : 0);
    response.avarageTime.TMF.push(tmf ? tmf : 0);
  });

  return response;
};

export const formatNumberForChart = (number: number) => {
  return number.toLocaleString();
}

const formatDateForThisChart = (date: string) => {
  const dateParts = date.split('-');

  if (dateParts.length !== 3) return date;

  const day = dateParts[2];
  const month = dateParts[1];
  return `${day}/${month}`;
}

const parseTimeToMinutes = (time: string) => {
  if (!time) return 0; // Handle empty time string

  const timeWithDaysRegex = /^(\d{1,5}d) ([0-5]\d):([0-5]\d)$/;
  if (timeWithDaysRegex.test(time)) {
    const [days, hours, minutes] = time.split(/d |:/).map(Number);
    return days * 24 * 60 + hours * 60 + minutes;
  }

  const parts = time.split(':');
  if (parts.length !== 3) return 0; // Unexpected time format

  const hours = parseInt(parts[0]);
  const minutes = parseInt(parts[1]);

  return hours * 60 + minutes;
};
