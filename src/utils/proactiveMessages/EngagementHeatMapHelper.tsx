import { capitalizeFirstLetter } from '@utils/text';

export interface EngagementHeatMapData {
  interval: number;
  data: number[][];
  lineLabels: string[];
}

export const formatEngagementHeatMapData = (data: string[][]): EngagementHeatMapData[] => {
  const filteredData = data.filter((dayData: string[]) => dayData[0] !== null);
  const lineLabels: string[] = [];
  const parsedData = filteredData.map((weekday: string[]) => {
    const weekdayName = weekday.shift() as string; // primeiro valor é o nome do dia ex.: domingo
    lineLabels.push(capitalizeFirstLetter(weekdayName));
    return weekday.map((valueHour) => parseFloat(valueHour));
  });

  if (parsedData.length === 0) {
    return [];
  }

  const min = Math.min(...parsedData.map((weekday: number[]) => Math.min(...weekday)));
  const max = Math.max(...parsedData.map((weekday: number[]) => Math.max(...weekday)));
  return [
    {
      data: parsedData,
      interval: max - min,
      lineLabels,
    },
  ];
};
