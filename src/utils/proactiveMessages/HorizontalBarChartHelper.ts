
export const formatDataToHorizontalBarChart = (data: string[][]) => {
  // let filteredData = data.filter((item: (string | null)[]) => !item.includes(null));
  const filteredData = data.filter((item: string[]) => parseFloat(item[1]) !== 0);
  const formatedData = filteredData.map((item: string[]) => {
    const label = item[0] === null ? 'N/A' : item[0] === '' ? 'Não informado' : item[0];
    return {
      label,
      value: parseFloat(item[1]),
      percentage: parseFloat(item[2])
    }
  })
  formatedData.sort((a, b) => b.value - a.value);
  return formatedData;
};

export const formatInteractionTypesChartData = (data: string[][]) => {
  const formatedData = formatDataToHorizontalBarChart(data);
  const formatedLabelData = formatedData.map((item) => {
    const labelTexts = item.label.split(',');
    if(labelTexts.length > 1 && labelTexts[1] !== ''){
      const lastLabelText = labelTexts.pop();
      return {
        ...item,
        label: `${labelTexts.join(', ')} e ${lastLabelText}`
      }
    }
    return item;
  });
  return formatedLabelData;
};


