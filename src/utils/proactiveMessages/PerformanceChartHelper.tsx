import { PerformanceChartType } from '@typings/ProactiveMessagesFilter';

export const formatPerformanceChartData = (data: string[]) => {
  data.sort((a, b) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });

  const graphData = {
    date_series: [],
    data: {
      messagesSent: [],
      messagesReceived: [],
      messagesResponded: [],
      responsesCount: [],
    },
  } as PerformanceChartType;
  data.forEach((item) => {
    graphData.date_series.push(formatDateForThisChart(item[0]));
    graphData.data.messagesSent.push(parseInt(item[1]));
    graphData.data.messagesReceived.push(parseInt(item[2]));
    graphData.data.messagesResponded.push(parseInt(item[3]));
    graphData.data.responsesCount.push(parseFloat(item[4]));
  });

  return graphData;
};

const formatDateForThisChart = (date: string) => {
  const dateParts = date.split('-');

  if (dateParts.length !== 3) return date;

  const day = dateParts[2];
  const month = dateParts[1];
  return `${day}/${month}`;
};
