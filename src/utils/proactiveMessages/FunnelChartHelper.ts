import { FunnelChartData } from '@typings/FunnelChartData';

const translation = {
  failure_rate: 'falha',
  received: 'recebimento',
  read: 'leitura',
  responded: 'resposta',
  conversion: 'conversão',
};

export const formatFunnelChartData = (data: any): FunnelChartData => {
  const { bars, indicators } = data;

  const funnelChartBarsData = {
    sentData: {
      data: bars.sent?.results[0][0] || bars.sent?.results[0],
      tooltip: bars.tooltips?.results[0][0] || bars.tooltips?.results[0],
    },
    receivedData: {
      data: bars.received?.results[0][0] || bars.received?.results[0],
      tooltip: bars.tooltips?.results[0][1] || bars.tooltips?.results[1],
    },
    readData: {
      data: bars.read.results[0][0] || bars.read.results[0],
      tooltip: bars.tooltips?.results[0][2] || bars.tooltips?.results[2],
    },
    respondedData: {
      data: bars.responded?.results[0][0] || bars.responded?.results[0],
      tooltip: bars.tooltips?.results[0][3] || bars.tooltips?.results[3],
    },
    conversionData: {
      data: bars.conversion?.results[0][0] || bars.conversion?.results[0],
      tooltip: bars.tooltips?.results[0][4] || bars.tooltips?.results[4] || '0',
    },
  };

  const indicatorsData = {
    sent: {
      status: indicators.sent.status?.results[0][0],
      variation: indicators.sent.variation?.results[0][1],
    },
    received: {
      status: indicators.received.status?.results[0][0],
      variation: indicators.received?.variation?.results[0][1],
    },
    read: {
      status: indicators.read.status?.results[0][0],
      variation: indicators.read.variation?.results[0][1],
    },
    responded: {
      status: indicators.responded.status?.results[0][0],
      variation: indicators.responded?.variation?.results[0][1],
    },
    conversion: {
      status: indicators.conversion?.status?.results[0][0],
      // variation: indicators.conversion?.results?[0][0],
    },
  };

  return {
    sent: {
      bars: {
        value: funnelChartBarsData.sentData.data,
        tooltip: funnelChartBarsData.sentData.tooltip?.toString(),
      },
      indicators: {
        status: `${indicatorsData.sent.status}`,
        variation: indicatorsData.sent.variation,
        revertVariationChipColor: true,
        label: translation.failure_rate,
      },
    },
    received: {
      bars: {
        value: funnelChartBarsData.receivedData.data,
        tooltip: funnelChartBarsData.receivedData.tooltip?.toString(),
      },
      indicators: {
        status: `${indicatorsData.received.status}`,
        variation: `${indicatorsData.received.variation}`,
        revertVariationChipColor: false,
        label: translation.received,
      },
    },
    read: {
      bars: {
        value: funnelChartBarsData.readData.data,
        tooltip: funnelChartBarsData.readData.tooltip?.toString(),
      },
      indicators: {
        status: `${indicatorsData.read.status}`,
        variation: `${indicatorsData.read.variation}`,
        revertVariationChipColor: false,
        label: translation.read,
      },
    },
    responded: {
      bars: {
        value: funnelChartBarsData.respondedData.data,
        tooltip: funnelChartBarsData.respondedData.tooltip?.toString(),
      },
      indicators: {
        status: `${indicatorsData.responded.status}`,
        variation: `${indicatorsData.responded.variation}`,
        revertVariationChipColor: false,
        label: translation.responded,
      },
    },
    conversion: {
      bars: {
        value: funnelChartBarsData.conversionData.data,
        tooltip: funnelChartBarsData.conversionData.tooltip?.toString(),
      },
      indicators: {
        status: '-1',
        variation: '',
        revertVariationChipColor: false,
        label: translation.conversion,
      },
    },
  };
};
