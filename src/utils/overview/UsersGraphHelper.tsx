import { UserGraphType } from '@typings/OverviewFilter';

export const formatUsersGraphData = (data: string[]) => {
  data.sort((a, b) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });

  const graphData = {
    date_series: [],
    data: {
      dailyActiveUsers: [],
      engagedUsersData: [],
      recurrencyRateData: [],
      engagementRateData: [],
    },
  } as UserGraphType;
  data.forEach((item) => {
    graphData.date_series.push(formatDateForThisChart(item[0]));
    graphData.data.dailyActiveUsers.push(parseInt(item[1]));
    graphData.data.engagedUsersData.push(parseInt(item[2]));
    graphData.data.recurrencyRateData.push(parseFloat(item[3]));
    graphData.data.engagementRateData.push(parseFloat(item[4]));
  });

  return graphData;
};

const formatDateForThisChart = (date: string) => {
  const dateParts = date.split('-');

  if (dateParts.length !== 3) return date;

  const day = dateParts[2];
  const month = dateParts[1];
  return `${day}/${month}`;
};
