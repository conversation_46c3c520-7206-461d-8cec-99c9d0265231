import { MessagesGraphType } from '@typings/OverviewFilter';

export const formatMessagesGraphData = (data: string[]) => {
  data.sort((a, b) => {
    const dateA = new Date(a[0]);
    const dateB = new Date(b[0]);
    return dateA.getTime() - dateB.getTime();
  });
  
  const graphData = {
    date_series: [],
    data: {
      sentMessages: [],
      receivedMessages: [],
      activeMessages: [],
      perUserMessage: [],
    },
  } as MessagesGraphType;
  data.forEach((item) => {
    graphData.date_series.push(formatDateForThisChart(item[0]));
    graphData.data.sentMessages.push(parseInt(item[1]));
    graphData.data.receivedMessages.push(parseInt(item[2]));
    graphData.data.activeMessages.push(parseFloat(item[3]));
    graphData.data.perUserMessage.push(parseFloat(item[4]));
  });

  return graphData;
};

const formatDateForThisChart = (date: string) => {
  const dateParts = date.split('-');

  if (dateParts.length !== 3) return date;

  const day = dateParts[2];
  const month = dateParts[1];
  return `${day}/${month}`;
};

