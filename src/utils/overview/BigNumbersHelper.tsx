import { parseTimeToSeconds } from '@utils/time';
import { TooltipPostionType } from 'blip-ds/dist/types/components/tooltip/tooltip';
import { BigNumberCardProps } from '@components/big_number_card';
import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
export interface BigNumberDetails {
  title: string;
  type: 'integer' | 'period' | 'percentage';
  tooltip: TooltipData;
  icon: string;
}

interface TooltipData {
  text: string;
  position: TooltipPostionType;
}

const NA_STRING = 'N/A';

const bigNumberDetails: BigNumberDetails[] = [
  {
    title: 'Usuários ativos acumulados',
    type: 'integer',
    tooltip: {
      text: 'Acumulado de usuários diários que enviaram ou receberam pelo menos uma mensagem do Contato Inteligente',
      position: 'top-left' as TooltipPostionType,
    },
    icon: 'user-default',
  },
  {
    title: 'Engajamento',
    type: 'percentage',
    tooltip: {
      text: 'Percentual de usuários que interagiram com o Contato Inteligente em relação aos usuários ativos',
      position: 'top-left' as TooltipPostionType,
    },
    icon: 'monitoring',
  },
  {
    title: 'Recorrência',
    type: 'percentage',
    tooltip: {
      text: 'Percentual de usuários que interagiram anteriormente em relação a todo o histórico do Contato Inteligente',
      position: 'top-left' as TooltipPostionType,
    },
    icon: 'calendar',
  },
  {
    title: 'Total de mensagens',
    type: 'integer',
    tooltip: {
      text: 'Total de mensagens enviadas e recebidas pelo Contato Inteligente no período',
      position: 'bottom-center' as TooltipPostionType,
    },
    icon: 'message-total',
  },
  {
    title: 'Mensagens por usuário',
    type: 'percentage',
    tooltip: {
      text: 'Média de mensagens enviadas por usuários engajados',
      position: 'top-left' as TooltipPostionType,
    },
    icon: 'user-engaged',
  },
  {
    title: 'Mensagens ativas',
    type: 'integer',
    tooltip: {
      text: 'Total de mensagens iniciadas pelo Contato Inteligente através de campanhas ou modelos de mensagem',
      position: 'top-left' as TooltipPostionType,
    },
    icon: 'message-read',
  },
];

export const formatBigNumbersData = (
  bigNumberCurrentPeriod: string[],
  bigNumberPreviousPeriod: string[]
): BigNumberCardProps[] => {
  const bigNumberData: BigNumberCardProps[] = [];
  for (let i = 0; i < bigNumberCurrentPeriod.length; i++) {
    const currentPeriod = bigNumberCurrentPeriod[i];
    const previousPeriod = bigNumberPreviousPeriod[i];

    const variation = calculateVariation(currentPeriod, previousPeriod);

    const formattedData: BigNumberCardProps = {
      type: bigNumberDetails[i].type,
      title: bigNumberDetails[i].title,
      variation: `${variation}${variation === NA_STRING ? '' : '%'}`,
      icon: bigNumberDetails[i].icon,
      tooltip: bigNumberDetails[i].tooltip,
      value: formatValue(currentPeriod, bigNumberDetails[i].type),
      chipColor: getChipColor(variation),
    };

    bigNumberData.push(formattedData);
  }
  return bigNumberData;
};

const getChipColor = (variation: string) => {
  if (!variation) return 'disabled';
  const varationInNumber = parseInt(variation.replace(/\D/g, ''));
  if (variation === NA_STRING || varationInNumber === 0) return 'disabled';
  if (variation.trim().startsWith('-')) return 'danger';
  return 'success';
};

const formatValue = (value: string, type: string) => {
  if (type === 'period') {
    return value;
  }

  if (type === 'percentage') {
    return `${value}%`;
  }

  if (isNaN(+value)) return value;

  return formatNumberForChart(parseInt(value));
};

const calculateVariation = (currentPeriod: string | number, previousPeriod: string | number) => {
  if (!currentPeriod || !previousPeriod) return NA_STRING;
  if (typeof currentPeriod === 'number') {
    currentPeriod = currentPeriod.toString();
  }
  if (typeof previousPeriod === 'number') {
    previousPeriod = previousPeriod.toString();
  }

  if (currentPeriod.includes(':') && previousPeriod.includes(':')) {
    return calculateTimeVariation(currentPeriod, previousPeriod);
  }
  return calculateNumberVariation(currentPeriod, previousPeriod);
};

const calculateTimeVariation = (currentPeriod: string, previousPeriod: string) => {
  const currentPeriodValue = parseTimeToSeconds(currentPeriod);
  const previousPeriodValue = parseTimeToSeconds(previousPeriod);

  return calculateNumberVariation(currentPeriodValue.toString(), previousPeriodValue.toString());
};

const calculateNumberVariation = (currentPeriod: string, previousPeriod: string) => {
  const currentPeriodValue = parseFloat(currentPeriod);
  const previousPeriodValue = parseFloat(previousPeriod);

  if (previousPeriodValue === 0) {
    return NA_STRING; // Avoid division by zero
  }

  const variation = ((currentPeriodValue - previousPeriodValue) / previousPeriodValue) * 100;
  return `${variation.toFixed(0)}`;
};
