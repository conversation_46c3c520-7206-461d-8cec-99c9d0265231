import { useEffect } from 'react';

import * as Sentry from '@sentry/react';
import { Client, ErrorEvent } from '@sentry/types';
import { createRoutesFromChildren, matchRoutes, useLocation, useNavigationType } from 'react-router-dom';

export interface SentryConfig {
  isEnabled: boolean;
  dsn: string;
  sampleRate: number;
  tracesSampleRate?: number;
  replaysSessionSampleRate?: number;
  replaysOnErrorSampleRate?: number;
}

export class SentryClient {
  private static sentryClient: Client | undefined;
  static startSentry(sentryConfig: SentryConfig, mfeBlobPath: string) {
    if (this.sentryClient) {
      console.warn('Monitoring tool already started');
      return;
    }
    try {
      this.sentryClient = SentryClient.getOrStartSentryClientInstance(sentryConfig, mfeBlobPath);
    } catch (error) {
      console.error('Error initializing monitoring tool', error);
    }
  }

  static getStartedSentryClient(): Client | undefined {
    if (this.sentryClient) {
      return this.sentryClient;
    }
    throw new Error('Monitoring tool not started');
  }

  static getOrStartSentryClientInstance(
    {
      dsn,
      sampleRate = 0.01,
      tracesSampleRate = 0,
      replaysSessionSampleRate = 0,
      replaysOnErrorSampleRate = 0,
    }: SentryConfig,
    mfeBlobPath: string
  ): Client | undefined {
    if (this.sentryClient) {
      return this.sentryClient;
    }
    this.sentryClient = Sentry.init({
      dsn,
      integrations: [
        Sentry.reactRouterV6BrowserTracingIntegration({
          useEffect,
          useLocation,
          useNavigationType,
          createRoutesFromChildren,
          matchRoutes,
        }),
        Sentry.replayIntegration(),
      ],
      sampleRate,
      tracesSampleRate,
      replaysSessionSampleRate,
      replaysOnErrorSampleRate,
      release: process.env.RELEASE_VERSION,
      beforeSend(event: ErrorEvent) {
        const frames = event.exception?.values?.[0]?.stacktrace?.frames;
        if (!frames) return null;
        const stacktraceString = frames.map((frame) => frame.filename).join('');
        return stacktraceString.includes(mfeBlobPath) ? event : null;
      },
    });
    console.log(`-- Sentry release version: ${process.env.RELEASE_VERSION} --`);
    return this.sentryClient;
  }
}
