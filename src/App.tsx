import React from 'react';
import '@styles/global.scss';

import { AppProvider } from './contexts/AppContext';
import { installDS } from './lib/DS';
import { Routes } from './Routes';
import { TranslationProvider } from '@hooks/useTranslation';
import { BlipInsightsServicesProvider } from '@hooks/useServices';
import { User } from '@typings/User';
import { BlipServiceProvider } from '@contexts/blip-service-context';

interface AppProps {
  language: 'pt' | 'en' | 'es';
  authtoken: string;
  currenttenantid: string;
  settings: string;
  user: User;
}

export const App = ({ language, authtoken, currenttenantid, settings, user }: AppProps) => {
  if (process.env.NODE_ENV === 'development') {
    installDS();
  }

  return (
    <TranslationProvider language={language ? language : 'pt'}>
      <AppProvider tenantId={currenttenantid} user={user} authtoken={authtoken} settings={settings}>
        <BlipInsightsServicesProvider authtoken={authtoken} settings={settings}>
          <Routes />
        </BlipInsightsServicesProvider>
      </AppProvider>
    </TranslationProvider>
  );
};
