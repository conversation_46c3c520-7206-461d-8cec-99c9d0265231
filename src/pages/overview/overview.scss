@import 'blip-ds/dist/collection/styles/_colors.scss';
@import '@components/filters/filters.scss';

.overview {
  display: flex;
  flex-direction: column;
  gap: var(--3, 24px);
  align-self: stretch;
  justify-content: flex-start;
  margin: 24px 0;
  min-height: 100vh;

  &__loading-overlay {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }

  &__big-numbers-grid {
    display: grid;
    gap: var(--1, 8px);
    grid-template-columns: repeat(3, 1fr);
    width: 100%;
  }

  &__filter-container {
    max-inline-size: fit-content;
    max-width: -webkit-fill-available;

    .filters__container-date {
      @include grid-template-columns-filter(3);
    }

    .filters__button-container {
      @include grid-column-span-filter(1);
    }
  }

  &__performance-graph-container {
    background-color: $color-surface-1;
    border-radius: var(--2, 16px);
    width: 100%;
    gap: var(--3, 24px);
    padding: var(--2, 16px);
    display: grid;
    grid-template-columns: repeat(12, 1fr);

    :nth-child(1) {
      grid-column: span 8;
    }
    :nth-child(2) {
      grid-column: span 4;
    }
  }

  &__no-data-message {
    > bds-typo {
      margin-top: var(--2, 32px);
      margin-bottom: var(--2, 32px);
      margin-left: var(--2, 32px);
    }

    background: var(--color-surface-1, #F6F6F6);
    border-radius: var(--2, 16px);
    min-height: 40px;
    grid-column-start: 1;
    grid-column-end: 4;
    display: flex;
  }
}

@media screen and (max-width: 1366px) {
  .overview {
    &__performance-graph-container {
      :nth-child(1) {
        grid-column: span 7;
      }
      :nth-child(2) {
        grid-column: span 5;
      }
    }
  }
}
