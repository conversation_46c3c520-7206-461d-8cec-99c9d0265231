import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';

import './overview.scss';

import { useBlipInsightsServices } from '@hooks/useServices';

import { useAppContext } from '@contexts/AppContext';

import BigNumberCard from '@components/big_number_card';
import FilterSelect, { FilterSelectOption } from '@components/filter_select';
import Filters from '@components/filters';
import { Insights } from '@components/insights';
import MessagesGraph from '@components/messages_graph';
import UsersGraph from '@components/users_graph';

import { BackendResponse } from '@services/BlipInsightsService';

import {
  mapInsightsToDatabricksInsigthsRequestData,
} from '@services/query/OverviewQueryBuilder';

import { formatToBrazilianDate } from '@utils/date';
import {
  finishedFetchLastDataUpdateEventName,
  finishedOnApplyFilterEventName,
  shallRefreshChartsEventName,
  startedFetchLastDataUpdateEventName,
  startedOnApplyFilterEventName,
} from '@utils/constants';
import { OverviewFilter } from '@typings/OverviewFilter';
import { ServiceRegistry } from '@services/registry/serviceRegistry';
import { BigNumbersOverviewService } from '@services/overview/BigNumbersOverviewService';
import { UsersGraphOverviewService } from '@services/overview/UsersGraphOverviewService';
import { AllChartsDataInterface } from '@typings/AllChartsDataInterface';
import { formatBigNumbersData } from '@utils/overview/BigNumbersHelper';
import { MessagesOverviewService } from '@services/overview/MessagesOverviewService';
import { toInsightsFilter } from '@utils/filters';
import { FiltersLoaderService } from '@services/filters/FiltersLoaderService';

const setDataForFilter = (
  response: BackendResponse,
  setFunction: ((channels: FilterSelectOption[]) => void) | undefined
) => {
  if (response?.results?.length) {
    const filterItemsFromResponse = response.results.map((item: any) => {
      return { label: item[0], value: item[0] };
    });
    if (setFunction) {
      setFunction(filterItemsFromResponse);
    }
  }
};

interface OverviewInsights {
  usersGraph: {
    insight_llm: string;
  };
  messagesGraph: {
    insight_llm: string;
  };
}

const serviceKeys = {
  BigNumbersOverviewService: 'BigNumbersOverviewService',
  BigNumbersOverviewServicePreviousPeriod: 'BigNumbersOverviewServicePreviousPeriod',
  UsersGraphOverviewService: 'UsersGraphOverviewService',
  MessagesOverviewService: 'MessagesOverviewService',
};

const responseKeys = {
  BigNumbersOverviewServiceResponse: 'BigNumbersOverviewServiceResponse',
  BigNumbersOverviewServicePreviousPeriodResponse: 'BigNumbersOverviewServicePreviousPeriodResponse',
  UsersGraphOverviewServiceResponse: 'UsersGraphOverviewServiceResponse',
  MessagesOverviewServiceResponse: 'MessagesOverviewServiceResponse',
};

export const Overview: React.FC = () => {
  const { tenantId } = useAppContext();
  const { blipInsightsService, apiBlipInsightsService } = useBlipInsightsServices();
  const [channels, setChannels] = useState<FilterSelectOption[]>([]);
  const [routerIds, setRouterIds] = useState<FilterSelectOption[]>([]);
  const [botIds, setBotIds] = useState<FilterSelectOption[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<OverviewFilter>({
    startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
    endDate: new Date(),
  } as OverviewFilter);
  const [isFiltersDataLoaded, setIsFiltersDataLoaded] = useState(false);
  const [applyButtonDisabled, setApplyButtonDisabled] = useState(false);
  const [clearButtonDisabled, setClearButtonDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isInsightsLoading, setIsInsightsLoading] = useState(false);
  const [initialFilterApplied, setInitialFilterApplied] = useState(false);
  const [overviewInsights, setOverviewInsights] = useState<OverviewInsights>({
    usersGraph: { insight_llm: '' },
    messagesGraph: { insight_llm: '' },
  });
  const serviceRegistry = useRef(new ServiceRegistry(apiBlipInsightsService, tenantId));
  const filtersLoaderService = useRef(new FiltersLoaderService());
  const [allChartsData, setAllChartsData] = useState<AllChartsDataInterface>({});

  const fetchOverviewInsights = useCallback(async () => {
    setIsInsightsLoading(true);
    try {
      const msgGraphParam = toInsightsFilter('messages_graph_insights', selectedFilter, tenantId);
      const usersGraphParam = toInsightsFilter('users_graph_insights', selectedFilter, tenantId);
      const [messagesGraphInsightsResponse, usersGraphInsightResponse] = await Promise.all([
        apiBlipInsightsService.getInsightsData(msgGraphParam),
        apiBlipInsightsService.getInsightsData(usersGraphParam),
      ]);
      const messagesInsightsData =
        (messagesGraphInsightsResponse?.results &&
          messagesGraphInsightsResponse?.results?.filter((i: any) => i !== null)) ||
        [];
      const usersInsightsData =
        (usersGraphInsightResponse?.results &&
          usersGraphInsightResponse?.results.filter((i: any) => i !== null)) ||
        [];
      const [msgGraphInsight, usersInsights] = await Promise.all([
        blipInsightsService.getInsightsData(
          mapInsightsToDatabricksInsigthsRequestData(messagesInsightsData, tenantId, selectedFilter, 'msgs')
        ),
        blipInsightsService.getInsightsData(
          mapInsightsToDatabricksInsigthsRequestData(usersInsightsData, tenantId, selectedFilter, 'users')
        ),
      ]);
      setOverviewInsights({
        messagesGraph: {
          insight_llm: msgGraphInsight.predictions.insight_llm,
        },
        usersGraph: {
          insight_llm: usersInsights.predictions.insight_llm,
        },
      });
    } catch (error) {
    } finally {
      setIsInsightsLoading(false);
    }
  }, [blipInsightsService, selectedFilter, tenantId, apiBlipInsightsService]);

  const startedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  const finishedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(false);
    setClearButtonDisabled(false);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  const fetchFiltersData = useCallback(async () => {
    const [fetchedChannels, fetchedRouters, fetchedBots] = await filtersLoaderService.current.getAllOverviewFiltersData(tenantId, apiBlipInsightsService);

    if (!channels?.length && fetchedChannels) {
      setDataForFilter(fetchedChannels, setChannels);
    }
    if (!routerIds?.length && fetchedRouters) {
      setDataForFilter(fetchedRouters, setRouterIds);
    }
    if (!botIds?.length && fetchedBots) {
      setDataForFilter(fetchedBots, setBotIds);
    }
    setIsFiltersDataLoaded(true);
  }, [botIds, channels, routerIds, setBotIds, setChannels, setRouterIds, apiBlipInsightsService, tenantId]);

  const onApplyFilter = useCallback(async () => {
    document.dispatchEvent(new CustomEvent(startedOnApplyFilterEventName));
    setIsLoading(true);
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
    try {
      fetchOverviewInsights();
      const allChartsDataResponse = await serviceRegistry.current.loadAllDataFromRegisteredServices(selectedFilter);
      setAllChartsData(allChartsDataResponse);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
      setApplyButtonDisabled(false);
      setClearButtonDisabled(false);
      document.dispatchEvent(new CustomEvent(finishedOnApplyFilterEventName));
    }
  }, [selectedFilter, serviceRegistry, fetchOverviewInsights]);

  const getDataFromService = useCallback((serviceKey: string, responseKey: string) => {
    const service = serviceRegistry.current.getService(serviceKey);
    if (!service) return null;
    const response = allChartsData[responseKey];
    return service.getFormattedData(response);
  }, [allChartsData, serviceRegistry]);
  
  const bigNumbersData = useMemo(() => {
    const currentData = getDataFromService(serviceKeys.BigNumbersOverviewService, responseKeys.BigNumbersOverviewServiceResponse) || [];
    const previousData = getDataFromService(serviceKeys.BigNumbersOverviewServicePreviousPeriod, responseKeys.BigNumbersOverviewServicePreviousPeriodResponse) || [];
    return formatBigNumbersData(currentData, previousData);
  }, [getDataFromService]);

  const usersGraphData = useMemo(() => {
    return getDataFromService(serviceKeys.UsersGraphOverviewService, responseKeys.UsersGraphOverviewServiceResponse);
  }, [getDataFromService]);

  const messagesGraphData = useMemo(() => {
    return getDataFromService(serviceKeys.MessagesOverviewService, responseKeys.MessagesOverviewServiceResponse);
  }, [getDataFromService]);

  useEffect(() => {
    serviceRegistry.current.clearRegistry();
    const isPreviousPeriodQuery = true;
    serviceRegistry.current.addServiceToRegistry(serviceKeys.BigNumbersOverviewService, new BigNumbersOverviewService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.BigNumbersOverviewServicePreviousPeriod, new BigNumbersOverviewService(isPreviousPeriodQuery));
    serviceRegistry.current.addServiceToRegistry(serviceKeys.UsersGraphOverviewService, new UsersGraphOverviewService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.MessagesOverviewService, new MessagesOverviewService());
  }, [serviceRegistry, blipInsightsService, tenantId]);

  useEffect(() => {
    return () => {
      document.removeEventListener(shallRefreshChartsEventName, onApplyFilter);
    };
  }, [onApplyFilter]);

  useEffect(() => {
    return () => {
      document.removeEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
      document.removeEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);
    };
  }, [startedFetchLastDateUpdate, finishedFetchLastDateUpdate]);

  useEffect(() => {
    if (channels?.length && routerIds?.length && botIds?.length) {
      setIsFiltersDataLoaded(true);
      return;
    }

    document.addEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
    document.addEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);

    fetchFiltersData();
  }, [
    blipInsightsService,
    channels,
    routerIds,
    botIds,
    onApplyFilter,
    fetchFiltersData,
    startedFetchLastDateUpdate,
    finishedFetchLastDateUpdate,
  ]);

  useEffect(() => {
    document.removeEventListener(shallRefreshChartsEventName, onApplyFilter);
    document.addEventListener(shallRefreshChartsEventName, onApplyFilter);
  }, [onApplyFilter]);

  useEffect(() => {
    if (!isFiltersDataLoaded) {
      return;
    }
    const applyFilterAsync = async () => {
      onApplyFilter();
    };
    if (!initialFilterApplied) {
      applyFilterAsync();
      setInitialFilterApplied(true);
    }
  }, [isFiltersDataLoaded, onApplyFilter, initialFilterApplied, setInitialFilterApplied]);

  const getSelectedValues = (item: CustomEvent) => {
    return Object.keys(item.detail.value)
      .map((e) => item.detail.value[e])
      .filter((e) => e.checked)
      .map((e) => e.value);
  };

  const onChangeChannels = (item: CustomEvent) => {
    selectedFilter.channels = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeRouters = (item: CustomEvent) => {
    selectedFilter.routerIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeBots = (item: CustomEvent) => {
    selectedFilter.botIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeStartDate = (item: CustomEvent) => {
    selectedFilter.startDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onChangeEndDate = (item: CustomEvent) => {
    selectedFilter.endDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const clearFilter = () => {
    const defaultFilter = {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      channels: [],
      routerIds: [],
      botIds: [],
    } as OverviewFilter;

    setSelectedFilter({ ...defaultFilter });

    const datepicker = document.querySelector('bds-datepicker');
    if (datepicker) {
      datepicker.valueDateSelected = undefined;
      datepicker.valueEndDateSelected = undefined;

      setTimeout(() => {
        datepicker.valueDateSelected = formatToBrazilianDate(defaultFilter.startDate);
        datepicker.valueEndDateSelected = formatToBrazilianDate(defaultFilter.endDate);
      }, 0);
    }

    document.querySelectorAll('bds-autocomplete').forEach((autocomplete) => {
      const shadowRoot = autocomplete.shadowRoot;
      if (shadowRoot) {
        const selectOptions = shadowRoot.querySelectorAll('bds-select-option');
        selectOptions.forEach((option) => {
          if (option.checked === true) {
            option.checked = false;
            option.dispatchEvent(new CustomEvent('optionChecked', { detail: { checked: false } }));
          }
        });
      }
    });
  };

  return (
    <bds-grid class="overview">
      <div className="overview__filter-container">
        <Filters
          clearFilter={clearFilter}
          onApplyFilter={onApplyFilter}
          startDate={formatToBrazilianDate(new Date(new Date().setDate(new Date().getDate() - 7)))}
          endDate={formatToBrazilianDate(new Date())}
          onStartDateChange={onChangeStartDate}
          onEndDateChange={onChangeEndDate}
          applyButtonDisabled={applyButtonDisabled}
          clearButtonDisabled={clearButtonDisabled}
        >
          <FilterSelect
            options={channels || []}
            label="Canal"
            onChange={onChangeChannels}
            placeholder="Todos"
            value={selectedFilter.channels}
          />
          <FilterSelect
            options={routerIds || []}
            label="ID do roteador"
            onChange={onChangeRouters}
            placeholder="Todos"
            value={selectedFilter.routerIds}
          />
          <FilterSelect
            options={botIds || []}
            label="ID do chatbot"
            onChange={onChangeBots}
            placeholder="Todos"
            value={selectedFilter.botIds}
          />
        </Filters>
      </div>

      {isLoading && (
        <div className="overview__loading-overlay">
          <bds-icon name="loading" size="medium"></bds-icon>
          <bds-typo> Carregando os dados...</bds-typo>
        </div>
      )}

      {!isLoading && (
        <>
          <div className="overview__big-numbers-grid">
            {bigNumbersData?.length === 0 && (
              <div className="overview__no-data-message">
                <bds-typo variant="fs-16" bold="bold">
                  Não há dados para o filtro selecionado
                </bds-typo>
              </div>
            )}
            {bigNumbersData.map((item, index) => (
              <BigNumberCard
                key={index}
                type="period"
                title={item.title}
                variation={item.variation}
                icon={item.icon}
                tooltip={{
                  text: item.tooltip.text,
                  position: (index + 1) % 3 === 0 ? 'bottom-right' : item.tooltip.position,
                }}
                value={item.value}
                chipColor={item.chipColor}
              />
            ))}
          </div>
          <div className="overview__performance-graph-container">
            <UsersGraph
              labelPeriods={usersGraphData.date_series}
              dailyActiveUsers={usersGraphData.data.dailyActiveUsers}
              engagedUsers={usersGraphData.data.engagedUsersData}
              engagementRate={usersGraphData.data.recurrencyRateData}
              recurrencyRate={usersGraphData.data.engagementRateData}
            />
            {(overviewInsights.usersGraph.insight_llm || isInsightsLoading) && (
              <Insights textInsight={overviewInsights.usersGraph.insight_llm} loading={isInsightsLoading}></Insights>
            )}
          </div>
          <div className="overview__performance-graph-container">
            <MessagesGraph
              labelPeriods={messagesGraphData.date_series}
              sentMessages={messagesGraphData.data.sentMessages}
              receivedMessages={messagesGraphData.data.receivedMessages}
              activeMessages={messagesGraphData.data.activeMessages}
              perUserMessage={messagesGraphData.data.perUserMessage}
            />
            {(overviewInsights.messagesGraph.insight_llm || isInsightsLoading) && (
              <Insights
                textInsight={overviewInsights.messagesGraph.insight_llm}
                loading={isInsightsLoading}
              ></Insights>
            )}
          </div>
        </>
      )}
    </bds-grid>
  );
};
