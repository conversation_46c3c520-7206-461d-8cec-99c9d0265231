.templates-comparing {
  display: flex;
  flex-direction: column;
  gap: var(--3, 24px);
  align-self: stretch;
  justify-content: flex-start;
  margin: 24px 0;
  min-height: 100vh;

  &__header {
    display: flex;
    justify-content: space-between;
    flex-direction: row;

    &--left {
      flex: 5;
    }
  }

  &__container {
    display: grid;
    gap: var(--2, 16px);
  }

  &__charts {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: var(--2, 16px);
    width: 100%;
  }

  &__chart {
    grid-column: span 6;
  }
}

.templates-comparing__title {
  vertical-align: middle;
  margin-bottom: 0.5rem;
  color: #333;
  font-family: 'Nunito Sans', sans-serif;
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
}

.templates-comparing__subtitle {
  font-size: 14px;
  color: #666;
  font-weight: 400;
  line-height: 22px;
  font-family: 'Nunito Sans', sans-serif;
}
