import FilterSelect, { FilterSelectOption } from '@components/filter_select';
import Filters from '@components/filters';
import { useAppContext } from '@contexts/AppContext';
import { TAB_NAMES, useTabContext } from '@contexts/TabsContext';
import { useBlipInsightsServices } from '@hooks/useServices';
import { useTranslation } from '@hooks/useTranslation';
import { TemplatesComparingFilter } from '@typings/TemplatesComparingFilter';
import { finishedOnApplyFilterEventName, startedOnApplyFilterEventName } from '@utils/constants';
import { formatToBrazilianDate } from '@utils/date';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { translation } from './i18n/translations';

import './templates_comparing.scss';
import { BackendResponse } from '@services/BlipInsightsService';
import ConversionBanner from '@components/minor_components/conversion-banner/conversion_banner';
import PerformanceGraphTemplatesComparing from '@components/performance_graph_templates_comparing';
import HorizontalBarChart from '@components/horizontal_bar_chart';
import { ServiceRegistry } from '@services/registry/serviceRegistry';
import { AnswerTypesService } from '@services/proactiveMessages/AnswerTypesService';
import FunnelChart from '@components/funnel_chart';
import { FunnelChartService } from '@services/proactiveMessages/FunnelChartService';
import { PercentageAnswerService } from '@services/proactiveMessages/PercentageAnswerService';
import { useTemplateContext } from '@contexts/TemplateContext';
import { FiltersLoaderService } from '@services/filters/FiltersLoaderService';

export interface AllChartsDataInterface {
  [key: string]: BackendResponse | undefined;
}

const serviceKeys = {
  AnswerTypesService: 'AnswerTypesService',
  FunnelChartService: 'FunnelChartService',
  PercentageAnswerService: 'PercentageAnswerService',
};

const ResponseKeys = {
  AnswerTypesServiceResponse: 'AnswerTypesServiceResponse',
  FunnelChartServiceResponse: 'FunnelChartServiceResponse',
  PercentageAnswerServiceResponse: 'PercentageAnswerServiceResponse',
  // Add other chart data keys as needed
};

const config = {
  A: {
    color: '#1968F0',
    minColor: '#B2DFFD', // Minimum color for the bars
  },
  B: {
    color: '#F06305',
    minColor: '#FCAA73', // Minimum color for the bars
  },
};

const setDataForFilter = (
  response: BackendResponse,
  setFunction: ((channels: FilterSelectOption[]) => void) | undefined
) => {
  if (response?.results.length) {
    const filterItemsFromResponse = response.results.map((item: any) => {
      return { label: item[0], value: item[0] };
    });
    if (setFunction) {
      setFunction(filterItemsFromResponse);
    }
  }
};

const TemplatesComparing: React.FC = () => {
  const { apiBlipInsightsService } = useBlipInsightsServices();
  const { tenantId } = useAppContext();
  const { filterTemplates, activeTemplateA, setActiveTemplateA, activeTemplateB, setActiveTemplateB } =
    useTemplateContext();
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [routerIds, setRouterIds] = useState<FilterSelectOption[]>([]);
  const [botIds, setBotIds] = useState<FilterSelectOption[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<TemplatesComparingFilter>({
    startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
    endDate: new Date(),
  } as TemplatesComparingFilter);
  const [isFiltersDataLoaded, setIsFiltersDataLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingA, setIsLoadingA] = useState(false);
  const [isLoadingB, setIsLoadingB] = useState(false);
  const [applyButtonDisabled, setApplyButtonDisabled] = useState(true);
  const [clearButtonDisabled, setClearButtonDisabled] = useState(true);
  const [chartsDataA, setChartsDataA] = useState<AllChartsDataInterface>({});
  const [chartsDataB, setChartsDataB] = useState<AllChartsDataInterface>({});
  const [serviceRegistry] = useState(new ServiceRegistry(apiBlipInsightsService, tenantId));
  const filtersLoaderService = useRef(new FiltersLoaderService());
  const { setActiveTab } = useTabContext();

  const templateRef = React.useRef({
    templateA: activeTemplateA,
    templateB: activeTemplateB,
  });

  const fetchFiltersData = useCallback(async () => {
    const [fetchedRouters, fetchedBots] = await filtersLoaderService.current.getAllProactiveMessagesFiltersDataForTemplatesComparing(tenantId, apiBlipInsightsService)

    if (!routerIds?.length && fetchedRouters) {
      setDataForFilter(fetchedRouters, setRouterIds);
    }
    if (!botIds?.length && fetchedBots) {
      setDataForFilter(fetchedBots, setBotIds);
    }

    setIsFiltersDataLoaded(true);
  }, [apiBlipInsightsService, routerIds, botIds, setRouterIds, setBotIds, tenantId]);

  const onApplyFilter = useCallback(
    async (target?: string) => {
      setApplyButtonDisabled(true);
      setClearButtonDisabled(true);

      const currentTemplateA = templateRef.current.templateA;
      const currentTemplateB = templateRef.current.templateB;

      if (target) {
        if (target === 'A') {
          setIsLoadingA(true);
          try {
            const chartsDataResponseA = await serviceRegistry.loadAllDataFromRegisteredServices({
              ...selectedFilter,
              template: [currentTemplateA],
            });
            setChartsDataA(chartsDataResponseA);
          } catch (error) {
            console.error('Error fetching template A data:', error);
          } finally {
            setIsLoadingA(false);
          }
        } else if (target === 'B') {
          setIsLoadingB(true);
          try {
            const chartsDataResponseB = await serviceRegistry.loadAllDataFromRegisteredServices({
              ...selectedFilter,
              template: [currentTemplateB],
            });
            setChartsDataB(chartsDataResponseB);
          } catch (error) {
            console.error('Error fetching template B data:', error);
          } finally {
            setIsLoadingB(false);
          }
        }

        setApplyButtonDisabled(false);
        setClearButtonDisabled(false);
        return;
      }

      document.dispatchEvent(new CustomEvent(startedOnApplyFilterEventName));
      setIsLoading(true);
      try {
        const chartsDataResponseA = await serviceRegistry.loadAllDataFromRegisteredServices({
          ...selectedFilter,
          template: [currentTemplateA],
        });
        setChartsDataA(chartsDataResponseA);

        const chartsDataResponseB = await serviceRegistry.loadAllDataFromRegisteredServices({
          ...selectedFilter,
          template: [currentTemplateB],
        });
        setChartsDataB(chartsDataResponseB);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
        setApplyButtonDisabled(false);
        setClearButtonDisabled(false);
        document.dispatchEvent(new CustomEvent(finishedOnApplyFilterEventName));
      }
    },
    [serviceRegistry, selectedFilter]
  );

  const onSelectTemplate = useCallback(
    (template: string, target: 'A' | 'B') => {
      if (target === 'A') {
        templateRef.current = {
          ...templateRef.current,
          templateA: template,
        };
        setActiveTemplateA(template);
      } else {
        templateRef.current = {
          ...templateRef.current,
          templateB: template,
        };
        setActiveTemplateB(template);
      }

      // Allow state updates to propagate before triggering the filter update
      setTimeout(() => {
        onApplyFilter(target);
      }, 0);
    },
    [setActiveTemplateA, setActiveTemplateB, onApplyFilter]
  );

  const clearFilter = () => {
    const defaultFilter = {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      routerIds: [],
      botIds: [],
      template: [],
    } as TemplatesComparingFilter;
    setSelectedFilter({ ...defaultFilter });

    const datepicker = document.querySelector('bds-datepicker');
    if (datepicker) {
      datepicker.valueDateSelected = undefined;
      datepicker.valueEndDateSelected = undefined;

      setTimeout(() => {
        datepicker.valueDateSelected = formatToBrazilianDate(defaultFilter.startDate);
        datepicker.valueEndDateSelected = formatToBrazilianDate(defaultFilter.endDate);
      }, 0);
    }
  };

  const onChangeStartDate = (item: CustomEvent) => {
    selectedFilter.startDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onChangeEndDate = (item: CustomEvent) => {
    selectedFilter.endDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const getSelectedValues = (item: CustomEvent) => {
    return Object.keys(item.detail.value)
      .map((e) => item.detail.value[e])
      .filter((e) => e.checked)
      .map((e) => e.value);
  };

  const onChangeRouters = (item: CustomEvent) => {
    selectedFilter.routerIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeBots = (item: CustomEvent) => {
    selectedFilter.botIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  useEffect(() => {
    serviceRegistry.clearRegistry();
    serviceRegistry.addServiceToRegistry('AnswerTypesService', new AnswerTypesService());
    serviceRegistry.addMultiServiceToRegistry('FunnelChartService', new FunnelChartService());
    serviceRegistry.addServiceToRegistry('PercentageAnswerService', new PercentageAnswerService());
  }, [serviceRegistry]);

  useEffect(() => {
    if (!isFiltersDataLoaded) {
      return;
    }
    const applyFilterAsync = async () => {
      onApplyFilter();
    };
    // applyFilterAsync is async to prevent layout shift
    applyFilterAsync();
  }, [isFiltersDataLoaded, onApplyFilter]);

  useEffect(() => {
    if (routerIds?.length && botIds?.length) {
      setIsFiltersDataLoaded(true);
      return;
    }

    fetchFiltersData();
  }, [apiBlipInsightsService, routerIds, botIds, fetchFiltersData, onApplyFilter, filtersLoaderService]);

  return (
    <bds-grid class="templates-comparing">
      <div className="templates-comparing__filter-container">
        <Filters
          clearFilter={clearFilter}
          onApplyFilter={onApplyFilter}
          startDate={formatToBrazilianDate(selectedFilter.startDate)}
          endDate={formatToBrazilianDate(new Date())}
          onStartDateChange={onChangeStartDate}
          onEndDateChange={onChangeEndDate}
          applyButtonDisabled={applyButtonDisabled}
          clearButtonDisabled={clearButtonDisabled}
        >
          <FilterSelect
            options={routerIds || []}
            label="ID do roteador"
            onChange={onChangeRouters}
            placeholder="Todos"
            value={selectedFilter.routerIds}
          />
          <FilterSelect
            options={botIds || []}
            label="ID do chatbot"
            onChange={onChangeBots}
            placeholder="Todos"
            value={selectedFilter.botIds}
          />
        </Filters>
      </div>
      <div className="templates-comparing__container">
        <div className="templates-comparing__header">
          <div className="templates-comparing__header--left">
            <div className="templates-comparing__title">{translations.title}</div>
            <div className="templates-comparing__subtitle">{translations.subtitle}</div>
          </div>
          <div className="templates-comparing__header--right">
            <bds-button
              class="funnel-chart-container__templates-comparing-button"
              onClick={() => setActiveTab(TAB_NAMES.proactiveMessages)}
              color="content"
              variant="tertiary"
              size="medium"
            >
              {translations.back_to_dashboard}
            </bds-button>
          </div>
        </div>
        <ConversionBanner />

        {isLoading && (
          <div className="helpdesks__loading-overlay">
            <bds-icon name="loading" size="medium"></bds-icon>
            <bds-typo> Carregando os dados...</bds-typo>
          </div>
        )}
        {!isLoading && (
          <>
            <div className="templates-comparing__charts">
              {!isLoadingA ? (
                <div className="templates-comparing__chart">
                  <FunnelChart
                    barsColor={config.A.color}
                    minColor={config.A.minColor}
                    title={translations.answerTypeChart.title}
                    description={translations.answerTypeChart.description}
                    compare={true}
                    banner={false}
                    select={[...filterTemplates]}
                    selectedOption={activeTemplateA}
                    selectOnChange={(value) => onSelectTemplate(value, 'A')}
                    datasets={serviceRegistry
                      .getMultiService(serviceKeys.FunnelChartService)
                      ?.getMultiFormattedData(
                        (chartsDataA[ResponseKeys.FunnelChartServiceResponse] as unknown as BackendResponse[]) ||
                          undefined
                      )}
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
              {!isLoadingB ? (
                <div className="templates-comparing__chart">
                  <FunnelChart
                    barsColor={config.B.color}
                    minColor={config.B.minColor}
                    title={translations.answerTypeChart.title}
                    description={translations.answerTypeChart.description}
                    compare={true}
                    banner={false}
                    select={[...filterTemplates]}
                    selectOnChange={(value) => onSelectTemplate(value, 'B')}
                    selectedOption={activeTemplateB}
                    datasets={serviceRegistry
                      .getMultiService(serviceKeys.FunnelChartService)
                      ?.getMultiFormattedData(
                        (chartsDataB[ResponseKeys.FunnelChartServiceResponse] as unknown as BackendResponse[]) ||
                          undefined
                      )}
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
              {!isLoadingA ? (
                <div className="templates-comparing__chart">
                  <PerformanceGraphTemplatesComparing
                    chartColor={config.A.color}
                    labelPeriods={
                      serviceRegistry
                        .getService(serviceKeys.PercentageAnswerService)
                        ?.getFormattedData(chartsDataA[ResponseKeys.PercentageAnswerServiceResponse])?.labels || []
                    }
                    percentageAnswered={
                      serviceRegistry
                        .getService(serviceKeys.PercentageAnswerService)
                        ?.getFormattedData(chartsDataA[ResponseKeys.PercentageAnswerServiceResponse])?.percentages || []
                    }
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
              {!isLoadingB ? (
                <div className="templates-comparing__chart">
                  <PerformanceGraphTemplatesComparing
                    chartColor={config.B.color}
                    labelPeriods={
                      serviceRegistry
                        .getService(serviceKeys.PercentageAnswerService)
                        ?.getFormattedData(chartsDataB[ResponseKeys.PercentageAnswerServiceResponse])?.labels || []
                    }
                    percentageAnswered={
                      serviceRegistry
                        .getService(serviceKeys.PercentageAnswerService)
                        ?.getFormattedData(chartsDataB[ResponseKeys.PercentageAnswerServiceResponse])?.percentages || []
                    }
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
            </div>
            <div className="templates-comparing__charts">
              {!isLoadingA ? (
                <div className="templates-comparing__chart">
                  <HorizontalBarChart
                    chartColor={config.A.color}
                    title={translations.answerTypeChart.title}
                    description={translations.answerTypeChart.description}
                    datasets={
                      serviceRegistry
                        .getService(serviceKeys.AnswerTypesService)
                        ?.getFormattedData(chartsDataA[ResponseKeys.AnswerTypesServiceResponse]) || []
                    }
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
              {!isLoadingB ? (
                <div className="templates-comparing__chart">
                  <HorizontalBarChart
                    chartColor={config.B.color}
                    title={translations.answerTypeChart.title}
                    description={translations.answerTypeChart.description}
                    datasets={
                      serviceRegistry
                        .getService(serviceKeys.AnswerTypesService)
                        ?.getFormattedData(chartsDataB[ResponseKeys.AnswerTypesServiceResponse]) || []
                    }
                  />
                </div>
              ) : (
                <div className="templates-comparing__chart">
                  <div className="helpdesks__loading-overlay">
                    <bds-icon name="loading" size="medium"></bds-icon>
                    <bds-typo> Carregando os dados...</bds-typo>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </bds-grid>
  );
};

export default TemplatesComparing;
