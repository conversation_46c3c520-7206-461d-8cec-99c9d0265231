export const translation = {
  pt: {
    noDataText: 'Não há dados para o filtro selecionado',
    answerTypeChart: {
      title: 'Tipo de resposta',
      description: 'Indicadores do tipo de respostas às mensagens ativas',
      labels: [
        'Texto médio (14 a 100 caracteres)',
        'Texto curto (até 13 caracteres)',
        'Texto longo (mais de 100 caracteres)',
        'Respostas automáticas',
        'Apenas link',
      ],
    },
    performanceChart: {
      title: 'Gráfico de Performance',
      options: {
        D: 'Dia',
        M: 'Mês',
        A: 'Ano'
      }
    },
    interactionTypesChart: {
      title: 'Tipos de interação',
      description: 'Resultados de desempenho por tipo de interação nas conversas',
    },
    filters: {
      tooltipText:
        'As informações de categoria do template e campanha são atualizadas com 1 dia de atraso. Se você criou uma notificação hoje e quer acompanhá-la, use o filtro por nome do template + data de hoje. ',
      weekdays: ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>uarta', 'Quinta', 'Sexta', '<PERSON><PERSON>bad<PERSON>'],
    },
    tableActiveMessagesPerformance: {
      title: 'Performance de mensagens ativas',
    },
    tableMessageModelFailure: {
      title: 'Falhas no modelo de mensagem',
    },
    tableUsersResponse: {
      title: 'Respostas dos usuários',
    },
  },
};
