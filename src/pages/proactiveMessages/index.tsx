import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { formatToBrazilianDate } from '@utils/date';
import './proactive_messages.scss';
import { useTemplateContext } from '@contexts/TemplateContext';

import FilterSelect, { FilterSelectOption } from '@components/filter_select';
import Filters from '@components/filters';
import { Insights } from '@components/insights';
import PerformanceGraphProactiveMessages from '@components/performance_graph_proactive_messages';
import { TableUsersResponse } from '@components/tables/users_response';
import { TableMessageModelFailure } from '@components/tables/message_model_failure';
import { TableActiveMessagePerformance } from '@components/tables/active_message_performance';
import HorizontalBarChart from '@components/horizontal_bar_chart';
import Funnel<PERSON>hart from '@components/funnel_chart';
import {
  getInsightsDataQuery,
  mapInsightsToDatabricksInsigthsRequestData,
} from '@services/query/ProactiveMessagesQueryBuilder';
import { ProactiveMessagesFilter } from '@typings/ProactiveMessagesFilter';
import { useBlipInsightsServices } from '@hooks/useServices';
import { useAppContext } from '@contexts/AppContext';
import { BackendResponse } from '@services/BlipInsightsService';
import {
  finishedFetchLastDataUpdateEventName,
  finishedOnApplyFilterEventName,
  shallRefreshChartsEventName,
  startedFetchLastDataUpdateEventName,
  startedOnApplyFilterEventName,
} from '@utils/constants';
import { ServiceRegistry } from '@services/registry/serviceRegistry';
import { AnswerTypesService } from '@services/proactiveMessages/AnswerTypesService';
import { PerformanceChartService } from '@services/proactiveMessages/PerformanceChartService';
import { InteractionTypesService } from '@services/proactiveMessages/InteractionTypeService';
import { UsersResponseService } from '@services/proactiveMessages/UsersResponseService';
import { ActiveMessagePerformanceService } from '@services/proactiveMessages/ActiveMessagePerformanceService';
import { MessageModelFailureService } from '@services/proactiveMessages/MessageModelFailureService';
import { HeatMapService } from '@services/proactiveMessages/HeatMapService';
import { FunnelChartService } from '@services/proactiveMessages/FunnelChartService';
import { EngagementVolumeHeatMap } from '@components/heat_map/engagement_volume';
import { FiltersLoaderService } from '@services/filters/FiltersLoaderService';
import { toInsightsFilter } from '@utils/filters';

export interface AllChartsDataInterface {
  [key: string]: BackendResponse | undefined;
}

const serviceKeys = {
  PerformanceChartService: 'PerformanceChartService',
  AnswerTypesService: 'AnswerTypesService',
  InteractionTypesService: 'InteractionTypesService',
  UsersResponseService: 'UsersResponseService',
  ActiveMessagePerformanceService: 'ActiveMessagePerformanceService',
  MessageModelFailureService: 'MessageModelFailureService',
  InsightsService: 'InsightsService',
  HeatMapService: 'HeatMapService',
  FunnelChartService: 'FunnelChartService',
};

const ResponseKeys = {
  PerformanceChartServiceResponse: 'PerformanceChartServiceResponse',
  AnswerTypesServiceResponse: 'AnswerTypesServiceResponse',
  InteractionTypesServiceResponse: 'InteractionTypesServiceResponse',
  UsersResponseServiceResponse: 'UsersResponseServiceResponse',
  ActiveMessagePerformanceServiceResponse: 'ActiveMessagePerformanceServiceResponse',
  MessageModelFailureServiceResponse: 'MessageModelFailureServiceResponse',
  InsightsServiceResponse: 'InsightsServiceResponse',
  HeatMapServiceResponse: 'HeatMapServiceResponse',
  FunnelChartServiceResponse: 'FunnelChartServiceResponse',
};

const setDataForFilter = (
  response: BackendResponse,
  setFunction: ((channels: FilterSelectOption[]) => void) | undefined
) => {
  if (response?.results?.length) {
    const filterItemsFromResponse = response.results.map((item: any) => {
      return { label: item[0], value: item[0] };
    });
    if (setFunction) {
      setFunction(filterItemsFromResponse);
    }
  }
};

export const ProactiveMessages: React.FC = () => {
  const { apiBlipInsightsService, blipInsightsService } = useBlipInsightsServices();
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [selectedFilter, setSelectedFilter] = useState<ProactiveMessagesFilter>({
    startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
    endDate: new Date(),
    routerIds: [],
    botIds: [],
    category: [],
    template: [],
    campaign: [],
  } as ProactiveMessagesFilter);

  const [isFiltersDataLoaded, setIsFiltersDataLoaded] = useState(false);
  const [applyButtonDisabled, setApplyButtonDisabled] = useState(true);
  const [clearButtonDisabled, setClearButtonDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isInsightsLoading, setIsInsightsLoading] = useState(false);
  const [proactiveMessagesInsights, setProactiveMessagesInsights] = useState<string>('');

  const { tenantId } = useAppContext();
  const { filterTemplates, setFilterTemplates, setActiveTemplateA, setActiveTemplateB } = useTemplateContext();

  const [allChartsData, setAllChartsData] = useState<AllChartsDataInterface>({});
  const serviceRegistry = useRef(new ServiceRegistry(apiBlipInsightsService, tenantId));
  const filtersLoaderService = useRef(new FiltersLoaderService());
  const [routerIds, setRouterIds] = useState<FilterSelectOption[]>([]);
  const [botIds, setBotIds] = useState<FilterSelectOption[]>([]);
  const [category, setCategory] = useState<FilterSelectOption[]>([]);
  const [template, setTemplate] = useState<FilterSelectOption[]>([]);
  const [campaign, setCampaign] = useState<FilterSelectOption[]>([]);

  const startedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  const finishedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(false);
    setClearButtonDisabled(false);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  const fetchProactiveMessagesInsights = useCallback(async () => {
    setIsInsightsLoading(true);
    try {
      const insightsRequestData = await apiBlipInsightsService.getData(toInsightsFilter('insights', selectedFilter, tenantId));

      const insightsData =
        (insightsRequestData?.results &&
          insightsRequestData?.results.filter((i: any) => i !== null)) ||
        [];

      const res = await blipInsightsService.getInsightsData(
        mapInsightsToDatabricksInsigthsRequestData(insightsData, tenantId, selectedFilter, 'notifications')
      );

      setProactiveMessagesInsights(res.predictions.insight_llm);
    } catch (error) {
      console.error('Error fetching proactive messages insights:', error);
    } finally {
      setIsInsightsLoading(false);
    }
  }, [apiBlipInsightsService, blipInsightsService, tenantId, selectedFilter]);

  const fetchFiltersData = useCallback(async () => {
    if (routerIds?.length && botIds?.length && category?.length && template?.length && campaign?.length) {
      return;
    }
    const [fetchedRouters, fetchedBots, fetchCategory, fetchedTemplate, fetchCampaign] =
      await filtersLoaderService.current.getAllProactiveMessagesFiltersData(tenantId, apiBlipInsightsService);

    if (!routerIds?.length && fetchedRouters) {
      setDataForFilter(fetchedRouters, setRouterIds);
    }
    if (!botIds?.length && fetchedBots) {
      setDataForFilter(fetchedBots, setBotIds);
    }
    if (!category?.length && fetchCategory) {
      setDataForFilter(fetchCategory, setCategory);
    }

    if (!template?.length && fetchedTemplate) {
      setDataForFilter(fetchedTemplate, setTemplate);
      const templates = fetchedTemplate.results.map((item: any) => ({
        label: item[0],
        value: item[0],
      }));
      setFilterTemplates(templates);
      setActiveTemplateA(templates[0].value);
      setActiveTemplateB(templates[1].value);
    }

    if (!campaign?.length && fetchCampaign) {
      setDataForFilter(fetchCampaign, setCampaign);
    }

    setIsFiltersDataLoaded(true);
  }, [
    routerIds,
    botIds,
    category,
    template,
    campaign,
    setRouterIds,
    setBotIds,
    setCategory,
    setTemplate,
    setCampaign,
    setFilterTemplates,
    setActiveTemplateA,
    setActiveTemplateB,
    filtersLoaderService,
    apiBlipInsightsService,
    tenantId,
  ]);

  const getSelectedValues = (item: CustomEvent) => {
    return Object.keys(item.detail.value)
      .map((e) => item.detail.value[e])
      .filter((e) => e.checked)
      .map((e) => e.value);
  };

  const onChangeRouters = (item: CustomEvent) => {
    selectedFilter.routerIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeBots = (item: CustomEvent) => {
    selectedFilter.botIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeCategory = (item: CustomEvent) => {
    selectedFilter.category = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeTemplate = (item: CustomEvent) => {
    selectedFilter.template = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeCampaign = (item: CustomEvent) => {
    selectedFilter.campaign = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeStartDate = (item: CustomEvent) => {
    selectedFilter.startDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onChangeEndDate = (item: CustomEvent) => {
    selectedFilter.endDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onApplyFilter = useCallback(async () => {
    document.dispatchEvent(new CustomEvent(startedOnApplyFilterEventName));
    setIsLoading(true);
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
    try {
      fetchProactiveMessagesInsights();
      const allChartsDataResponse = await serviceRegistry.current.loadAllDataFromRegisteredServices(selectedFilter);
      setAllChartsData(allChartsDataResponse);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
      setApplyButtonDisabled(false);
      setClearButtonDisabled(false);
      document.dispatchEvent(new CustomEvent(finishedOnApplyFilterEventName));
    }
  }, [fetchProactiveMessagesInsights, selectedFilter, serviceRegistry]);

  const clearFilter = () => {
    const defaultFilter = {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      routerIds: [],
      botIds: [],
      category: [],
      template: [],
      campaign: [],
    } as ProactiveMessagesFilter;

    setSelectedFilter({ ...defaultFilter });

    const datepicker = document.querySelector('bds-datepicker');
    if (datepicker) {
      datepicker.valueDateSelected = undefined;
      datepicker.valueEndDateSelected = undefined;

      setTimeout(() => {
        datepicker.valueDateSelected = formatToBrazilianDate(defaultFilter.startDate);
        datepicker.valueEndDateSelected = formatToBrazilianDate(defaultFilter.endDate);
      }, 0);
    }

    document.querySelectorAll('bds-autocomplete').forEach((autocomplete) => {
      const shadowRoot = autocomplete.shadowRoot;
      if (shadowRoot) {
        const selectOptions = shadowRoot.querySelectorAll('bds-select-option');
        selectOptions.forEach((option) => {
          if (option.checked === true) {
            option.checked = false;
            option.dispatchEvent(new CustomEvent('optionChecked', { detail: { checked: false } }));
          }
        });
      }
    });
  };

  const getDataFromService = useCallback(
    (serviceKey: string, responseKey: string) => {
      const service = serviceRegistry.current.getService(serviceKey);
      if (!service) return null;
      const response = allChartsData[responseKey];
      return service.getFormattedData(response);
    },
    [allChartsData, serviceRegistry]
  );

  const engagementHeatMapData = useMemo(() => {
    return getDataFromService(serviceKeys.HeatMapService, ResponseKeys.HeatMapServiceResponse);
  }, [getDataFromService]);

  const answerTypeChartData = useMemo(() => {
    return getDataFromService(serviceKeys.AnswerTypesService, ResponseKeys.AnswerTypesServiceResponse);
  }, [getDataFromService]);

  const interactionTypesChartData = useMemo(() => {
    return getDataFromService(serviceKeys.InteractionTypesService, ResponseKeys.InteractionTypesServiceResponse);
  }, [getDataFromService]);

  const usersResponseData = useMemo(() => {
    return getDataFromService(serviceKeys.UsersResponseService, ResponseKeys.UsersResponseServiceResponse);
  }, [getDataFromService]);

  const messageModelFailureData = useMemo(() => {
    return getDataFromService(serviceKeys.MessageModelFailureService, ResponseKeys.MessageModelFailureServiceResponse);
  }, [getDataFromService]);

  const activeMessagePerformanceData = useMemo(() => {
    return getDataFromService(
      serviceKeys.ActiveMessagePerformanceService,
      ResponseKeys.ActiveMessagePerformanceServiceResponse
    );
  }, [getDataFromService]);

  const sortedTemplates = useMemo(() => {
    if (!activeMessagePerformanceData?.data?.length || activeMessagePerformanceData.data.length < 2) {
      return filterTemplates;
    }

    const sortedData = [...activeMessagePerformanceData.data].sort((a: any, b: any) => Number(b[1]) - Number(a[1]));
    const templates = [...filterTemplates];

    return sortTemplatesByPerformance(sortedData, templates);
  }, [activeMessagePerformanceData, filterTemplates]);

  useEffect(() => {
    if (sortedTemplates !== filterTemplates && 
        JSON.stringify(sortedTemplates.map(t => t.value)) !== 
        JSON.stringify(filterTemplates.map(t => t.value))) {
      setFilterTemplates(sortedTemplates);
    }
  }, [sortedTemplates, filterTemplates, setFilterTemplates]);

  useEffect(() => {
    if (activeMessagePerformanceData?.data?.length >= 2) {
      const sortedData = [...activeMessagePerformanceData.data].sort((a: any, b: any) => Number(b[1]) - Number(a[1]));
      setActiveTemplateA(sortedData[0][0]);
      setActiveTemplateB(sortedData[1][0]);
    }
  }, [activeMessagePerformanceData, setActiveTemplateA, setActiveTemplateB]);

  useEffect(() => {
    serviceRegistry.current.clearRegistry();
    serviceRegistry.current.addServiceToRegistry(serviceKeys.PerformanceChartService, new PerformanceChartService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.AnswerTypesService, new AnswerTypesService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.InteractionTypesService, new InteractionTypesService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.UsersResponseService, new UsersResponseService());
    serviceRegistry.current.addServiceToRegistry(
      serviceKeys.ActiveMessagePerformanceService,
      new ActiveMessagePerformanceService()
    );
    serviceRegistry.current.addServiceToRegistry(
      serviceKeys.MessageModelFailureService,
      new MessageModelFailureService()
    );
    serviceRegistry.current.addServiceToRegistry(serviceKeys.HeatMapService, new HeatMapService());
    serviceRegistry.current.addMultiServiceToRegistry(serviceKeys.FunnelChartService, new FunnelChartService());
  }, [serviceRegistry]);

  useEffect(() => {
    return () => {
      document.removeEventListener(shallRefreshChartsEventName, onApplyFilter);
    };
  }, [onApplyFilter]);

  useEffect(() => {
    return () => {
      document.removeEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
      document.removeEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);
    };
  }, [startedFetchLastDateUpdate, finishedFetchLastDateUpdate]);

  useEffect(() => {
    document.addEventListener(shallRefreshChartsEventName, onApplyFilter);
    document.addEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
    document.addEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);

    if (routerIds?.length && botIds?.length && category?.length && template?.length && campaign?.length) {
      setIsFiltersDataLoaded(true);
      return;
    }

    fetchFiltersData();
  }, [
    blipInsightsService,
    routerIds,
    botIds,
    category,
    template,
    campaign,
    fetchFiltersData,
    onApplyFilter,
    startedFetchLastDateUpdate,
    finishedFetchLastDateUpdate,
  ]);

  useEffect(() => {
    if (!isFiltersDataLoaded) {
      return;
    }
    const applyFilterAsync = async () => {
      onApplyFilter();
    };
    // applyFilterAsync is async to prevent layout shift
    applyFilterAsync();
  }, [isFiltersDataLoaded, onApplyFilter]);

  return (
    <bds-grid class="proactive-messages">
      <div className="proactive-messages__filter-container">
        <Filters
          clearFilter={clearFilter}
          onApplyFilter={onApplyFilter}
          startDate={formatToBrazilianDate(new Date(new Date().setDate(new Date().getDate() - 7)))}
          endDate={formatToBrazilianDate(new Date())}
          onStartDateChange={onChangeStartDate}
          onEndDateChange={onChangeEndDate}
          applyButtonDisabled={applyButtonDisabled}
          clearButtonDisabled={clearButtonDisabled}
          hasTooltip
          tooltipText={translations.filters.tooltipText}
        >
          <FilterSelect
            options={routerIds || []}
            label="ID do roteador"
            onChange={onChangeRouters}
            placeholder="Todos"
            value={selectedFilter.routerIds}
          />
          <FilterSelect
            options={botIds || []}
            label="ID do chatbot"
            onChange={onChangeBots}
            placeholder="Todos"
            value={selectedFilter.botIds}
          />
          <FilterSelect
            options={category || []}
            label="Categoria do modelo"
            onChange={onChangeCategory}
            placeholder="Todos"
            value={selectedFilter.category}
          />
          <FilterSelect
            options={template || []}
            label="Modelo"
            onChange={onChangeTemplate}
            placeholder="Todos"
            value={selectedFilter.template}
          />
          <FilterSelect
            options={campaign || []}
            label="Campanha"
            onChange={onChangeCampaign}
            placeholder="Todos"
            value={selectedFilter.campaign}
          />
        </Filters>
      </div>

      {isLoading && (
        <div className="helpdesks__loading-overlay">
          <bds-icon name="loading" size="medium"></bds-icon>
          <bds-typo> Carregando os dados...</bds-typo>
        </div>
      )}
      {!isLoading && (
        <>
          <div className="proactive-messages__performance-graph-container">
            <PerformanceGraphProactiveMessages
              labelPeriods={
                serviceRegistry.current
                  .getService(serviceKeys.PerformanceChartService)
                  ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.date_series || []
              }
              messagesSent={
                serviceRegistry.current
                  .getService(serviceKeys.PerformanceChartService)
                  ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.data.messagesSent ||
                []
              }
              messagesReceived={
                serviceRegistry.current
                  .getService(serviceKeys.PerformanceChartService)
                  ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.data
                  .messagesReceived || []
              }
              messagesResponded={
                serviceRegistry.current
                  .getService(serviceKeys.PerformanceChartService)
                  ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.data
                  .messagesResponded || []
              }
              responsesCount={
                serviceRegistry.current
                  .getService(serviceKeys.PerformanceChartService)
                  ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.data
                  .responsesCount || []
              }
            />
            {serviceRegistry.current
              .getService(serviceKeys.PerformanceChartService)
              ?.getFormattedData(allChartsData[ResponseKeys.PerformanceChartServiceResponse])?.data.messagesSent
              ?.length > 0 &&
              (proactiveMessagesInsights.length || isInsightsLoading) && (
              <Insights textInsight={proactiveMessagesInsights} loading={isInsightsLoading}></Insights>
            )}
          </div>
          <FunnelChart
            datasets={serviceRegistry.current
              .getMultiService(serviceKeys.FunnelChartService)
              ?.getMultiFormattedData(
                (allChartsData[ResponseKeys.FunnelChartServiceResponse] as unknown as BackendResponse[]) || undefined
              )}
            showCompareButton={true}
            compare={false}
          ></FunnelChart>

          <div className="helpdesks-performance__answers_type_chart">
            <HorizontalBarChart
              title={translations.answerTypeChart.title}
              description={translations.answerTypeChart.description}
              datasets={answerTypeChartData || []}
            />
          </div>
          <div className="proactive-messages__users-response-table-container">
            <TableUsersResponse data={usersResponseData?.data || []}></TableUsersResponse>
          </div>
          <div className="helpdesks-performance__answers_type_chart">
            <HorizontalBarChart
              title={translations.interactionTypesChart.title}
              description={translations.interactionTypesChart.description}
              datasets={interactionTypesChartData || []}
            />
          </div>
          <div className="proactive-messages__message-model-failure-table-container">
            <TableMessageModelFailure data={messageModelFailureData?.data || []}></TableMessageModelFailure>
          </div>
          <div className="helpdesks-performance__engagement_heatmap">
            <EngagementVolumeHeatMap
              receiptData={engagementHeatMapData || []}
              tenantId={tenantId}
              selectedFilter={selectedFilter}
            />
          </div>
          <div className="proactive-messages__active-message-performance-table-container">
            <TableActiveMessagePerformance
              data={activeMessagePerformanceData?.data || []}
            ></TableActiveMessagePerformance>
          </div>
        </>
      )}
    </bds-grid>
  );
};

/**
 * Sorts template options based on the order of templates in sortedData
 * @param sortedData - Array of template data sorted by performance (expecting [templateName, value] format)
 * @param filterTemplates - Array of template options to sort
 * @returns A new sorted array of template options, or the original array if no change is needed
 */
export const sortTemplatesByPerformance = (
  sortedData: Array<any>,
  filterTemplates: Array<any>
): Array<{ label: string; value: string | number | boolean }> => {
  if (!sortedData?.length || !filterTemplates?.length) {
    return filterTemplates;
  }

  // Create a position map for more efficient sorting
  const templatePositions = new Map();
  sortedData.forEach((item: any, index: number) => {
    templatePositions.set(item[0], index);
  });

  // Sort templates based on their positions in sortedData
  const sortedTemplates = [...filterTemplates].sort((a, b) => {
    const posA = templatePositions.has(a.value) ? templatePositions.get(a.value) : Number.MAX_SAFE_INTEGER;
    const posB = templatePositions.has(b.value) ? templatePositions.get(b.value) : Number.MAX_SAFE_INTEGER;
    return posA - posB;
  });

  // Check if we actually need to update
  const needsUpdate = JSON.stringify(sortedTemplates) !== JSON.stringify(filterTemplates);

  // Return the new sorted array only if it's different from the input
  return needsUpdate ? sortedTemplates : filterTemplates;
};
