@import '~blip-ds/dist/collection/styles/_colors.scss';
@import '@components/filters/filters.scss';

.proactive-messages {
  display: flex;
  flex-direction: column;
  gap: var(--3, 24px);
  align-self: stretch;
  justify-content: flex-start;
  margin: 24px 0;
  min-height: 100vh;

  &__filter-container {
    max-width: -webkit-fill-available;

    .filters__container-date {
      @include grid-template-columns-filter(5);
    }
  }

  &__engagement_heatmap {
    max-inline-size: fit-content;
    max-width: -webkit-fill-available;
  }

  &__answers_type_chart {
    max-inline-size: fit-content;
    max-width: -webkit-fill-available;
  }

  &__performance-graph-container {
    background-color: $color-surface-1;
    border-radius: var(--2, 16px);
    width: 100%;
    gap: var(--3, 24px);
    padding: var(--2, 16px);
    display: grid;
    grid-template-columns: repeat(12, 1fr);

    :nth-child(1) {
      grid-column: span 8;
    }
    :nth-child(2) {
      grid-column: span 4;
    }
  }
}

@media screen and (max-width: 1366px) {
  .proactive-messages {
    &__performance-graph-container {
      :nth-child(1) {
        grid-column: span 7;
      }
      :nth-child(2) {
        grid-column: span 5;
      }
    }
  }
}
