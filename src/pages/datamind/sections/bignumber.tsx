import { BigNumberCard } from '../components/bigNumberCard';
import React, { useEffect, useState, useCallback } from 'react';
import { useDatamind } from '../contexts/DatamindContext';
import { dataAnalyticsService } from '../services/DataAnalyticsService';
import { useAppContext } from '@contexts/AppContext';

interface SummaryDisplayData {
  totalConversas: string;
  satisfacao: string;
  erros: string;
}

export const BigNumberSection = () => {
  const { dateFilter } = useDatamind();
  const { tenantId } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [summaryData, setSummaryData] = useState<SummaryDisplayData>({
    totalConversas: '0',
    satisfacao: '0',
    erros: '0',
  });

  const fetchSummaryCounts = useCallback(async () => {
    if (!dateFilter.startDate || !dateFilter.endDate || !tenantId) {
      return;
    }

    setIsLoading(true);
    try {
      const dataInicial = dateFilter.startDate;
      const dataFinal = dateFilter.endDate;

      const response = await dataAnalyticsService.getSummaryCounts({
        dataInicial,
        dataFinal,
        tenantId: tenantId,
      });

      setSummaryData({
        totalConversas: response.totalConversas.toString(),
        satisfacao: response.satisfacao.toString(),
        erros: response.erros.toString(),
      });
    } catch (error) {
      console.error('Erro ao buscar dados do summary/counts:', error);

      setSummaryData({
        totalConversas: '0',
        satisfacao: '0',
        erros: '0',
      });
    } finally {
      setIsLoading(false);
    }
  }, [dateFilter.startDate, dateFilter.endDate, tenantId]);

  useEffect(() => {
    if (dateFilter.startDate && dateFilter.endDate && tenantId) {
      fetchSummaryCounts();
    }
  }, [dateFilter.startDate, dateFilter.endDate, tenantId, fetchSummaryCounts]);

  // Verifica se há dados válidos para exibir (pelo menos um valor diferente de 0)
  const hasValidData = () => {
    const totalConversas = parseInt(summaryData.totalConversas) || 0;
    const satisfacao = parseInt(summaryData.satisfacao) || 0;
    const erros = parseInt(summaryData.erros) || 0;
    
    return totalConversas > 0 || satisfacao > 0 || erros > 0;
  };

  // Se estiver carregando, mostra apenas o spinner
  if (isLoading) {
    return (
      <bds-grid direction="row" justify-content="center" padding="y-3">
        <bds-loading-spinner></bds-loading-spinner>
      </bds-grid>
    );
  }

  // Não renderiza a seção se não houver dados válidos
  if (!hasValidData()) {
    return null;
  }

  return (
    <bds-grid direction="row">
      <bds-grid md="4" sm="4" padding="l-none">
        <BigNumberCard
          title="Total de conversas analisadas"
          icon="message-total"
          tooltip={{
            text: `Período: ${dateFilter.startDate || ''} - ${dateFilter.endDate || ''}`,
            position: 'bottom-right',
          }}
          value={summaryData.totalConversas}
          className="papper-default"
        />
      </bds-grid>
      <bds-grid md="4" sm="4" padding="none">
        <BigNumberCard
          title="Análises de sentimento"
          icon="blip-ideas"
          tooltip={{
            text: `Período: ${dateFilter.startDate || ''} - ${dateFilter.endDate || ''}`,
            position: 'bottom-right',
          }}
          value={summaryData.satisfacao}
          className="papper-default"
        />
      </bds-grid>
      <bds-grid md="4" sm="4" padding="r-none">
        <BigNumberCard
          title="Erros"
          icon="warning"
          tooltip={{
            text: `Período: ${dateFilter.startDate || ''} - ${dateFilter.endDate || ''}`,
            position: 'bottom-right',
          }}
          value={summaryData.erros}
          className="papper-default"
        />
      </bds-grid>
    </bds-grid>
  );
};
