import React, { useCallback, useEffect, useState } from 'react';
import { HeaderCard } from '../components/headerCard';
import { BdsPagination } from 'blip-ds/dist/blip-ds-react/components';
import { dataAnalyticsService } from '../services/DataAnalyticsService';
import { useDatamind } from '../contexts/DatamindContext';
import { useAppContext } from '@contexts/AppContext';

export interface ListSuggestion {
  id: number;
  userId: string;
  date: string;
  type: string[][];
}

const heading = ['Descrição', 'Motivo'];

export interface SentimentContact {
  type: any;
  id: number;
  userId: string;
  date: string;
  sentiment_status: 'Bom' | 'Ruim' | 'Neutro';
}

export const SuggestionsSection = ({ className }: { className?: string }) => {
  const { dateFilter } = useDatamind();
  const { tenantId } = useAppContext();
  const [contactData, setContactData] = useState<SentimentContact[] | null>(null);
  const [isLoadingContacts, setIsLoadingContacts] = useState(false);
  const [pageInfo, setPageInfo] = useState({
    total: 0,
    page: 1,
    itemsPerPage: 4,
    pages: 0,
  });

  const handlePaginationChange = useCallback((event: any) => {
    setPageInfo(prev => ({
      ...prev,
      page: event.detail,
    }));
  }, []);

  const fetchLineChart = useCallback(async () => {
    if (!dateFilter.startDate || !dateFilter.endDate || !tenantId) {
      return;
    }

    setIsLoadingContacts(true);

    try {
      const dataConversation = await dataAnalyticsService.getConversationData({
        dataInicial: dateFilter.startDate,
        dataFinal: dateFilter.endDate,
        tenantId: tenantId,
        typeEnrichment: 'datamind_error',
        page: pageInfo.page,
        itemsPerPage: pageInfo.itemsPerPage,
      });

      setPageInfo(prev => ({
        ...prev,
        total: dataConversation.total,
        page: dataConversation.page,
        itemsPerPage: dataConversation.itemsPerPage,
        pages: dataConversation.pages,
      }));

      setContactData(dataConversation.data);

    } catch (error) {
      console.error('Erro ao buscar dados do gráfico de sentimento:', error);
    } finally {
      setIsLoadingContacts(false);
    }
  }, [dateFilter.startDate, dateFilter.endDate, tenantId, pageInfo.page, pageInfo.itemsPerPage]);

  useEffect(() => {
    if (dateFilter.startDate && dateFilter.endDate && tenantId) {
      fetchLineChart();
    }
  }, [fetchLineChart, dateFilter.startDate, dateFilter.endDate, tenantId]);

  return (
    <>
      <bds-paper class={className}>
        <bds-grid gap="2" padding="2" direction="column" flexWrap="wrap">
          <HeaderCard
            title="Sugestões no período"
            subtitle="Sugestões geradas através da análise das conversas utilizando inteligência artificial. Resultados podem ser imprecisos."
          />
          <div className="table-container">
            {isLoadingContacts ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                <bds-loading-spinner />
              </div>
            ) : (
              <>
                {contactData && contactData.length > 0 ? (
                  <bds-table style={{ border: 'none' }}>
                    <bds-table-header>
                      <bds-table-row>
                        {heading.map((item, index) => (
                          <bds-table-th key={index}>{item}</bds-table-th>
                        ))}
                      </bds-table-row>
                    </bds-table-header>
                    <bds-table-body>
                      {contactData.map((item, index) => (
                        <bds-table-row key={`${item.id}-${index}`}>
                          <bds-table-cell>
                            {item.type?.[0]?.[0] || '-'}
                          </bds-table-cell>
                          <bds-table-cell>
                            {item.type?.[0]?.[1] || '-'}
                          </bds-table-cell>
                        </bds-table-row>
                      ))}
                    </bds-table-body>
                  </bds-table>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px', color: '#666', fontSize: '16px' }}>
                    Nenhuma sugestão encontrada para o período selecionado
                  </div>
                )}
              </>
            )}
            {contactData && contactData.length > 0 && (
              <BdsPagination
                pages={pageInfo.pages}
                startedPage={pageInfo.page}
                numberItems={pageInfo.total}
                itemsPage={[4, 10, 20, 50]}
                pageCounter={true}
                onBdsPaginationChange={handlePaginationChange}
                onBdsItemsPerPageChange={(event) => {
                  setPageInfo(prev => ({
                    ...prev,
                    itemsPerPage: event.detail
                  }));
                }}
              />
            )}
          </div>
        </bds-grid>
      </bds-paper>
    </>
  );
};
