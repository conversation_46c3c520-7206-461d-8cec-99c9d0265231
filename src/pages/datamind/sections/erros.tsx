import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, LineChart } from '../components/charts';
import type { LineChartData } from '../components/charts';
import { HeaderCard } from '../components/headerCard';
import { ContactCard } from '../components/contactCard';
import { useSidebar } from '../contexts/SidebarContext';
import { BdsPagination } from 'blip-ds/dist/blip-ds-react/components';
import Bar<PERSON><PERSON>, { BarChartData } from '../components/charts/BarChart';
import { useDatamind } from '../contexts/DatamindContext';
import { dataAnalyticsService } from '../services/DataAnalyticsService';
import { useAppContext } from '@contexts/AppContext';

export interface SentimentContact {
  id: number;
  userId: string;
  botId: string;
  date: string;
  type: string[][];
}

const ErrosUserList = () => {
  const { dateFilter } = useDatamind();
  const { tenantId } = useAppContext();
  const [contactData, setContactData] = useState<SentimentContact[] | null>(null);
  const [isLoadingContacts, setIsLoadingContacts] = useState(false);
  const [pageInfo, setPageInfo] = useState({
    total: 0,
    page: 1,
    itemsPerPage: 4,
    pages: 0,
  });

  const handlePaginationChange = (event: any) => {
    setPageInfo({
      ...pageInfo,
      page: event.detail,
    });
  };

  const formatLinkParameters = (botId: string, userId: string) => {
    const cleanBotId = botId.replace('@msging.net', '');
    
    const numbersOnly = userId.replace(/\D/g, '');
    const formattedUserId = numbersOnly.length >= 10 && numbersOnly.length <= 13 
      ? `${userId}@wa.gw.msging.net` 
      : `${userId}@0mn.io`;
    
    return { cleanBotId, formattedUserId };
  };

  const fetchLineChart = useCallback(async () => {
    if (!dateFilter.startDate || !dateFilter.endDate || !tenantId) {
      return;
    }

    setIsLoadingContacts(true);

    try {
      const dataConversation = await dataAnalyticsService.getConversationData({
        dataInicial: dateFilter.startDate,
        dataFinal: dateFilter.endDate,
        tenantId: tenantId,
        typeEnrichment: 'datamind_error',
        page: pageInfo.page,
        itemsPerPage: pageInfo.itemsPerPage,
      })

      setPageInfo({
        total: dataConversation.total,
        page: dataConversation.page,
        itemsPerPage: dataConversation.itemsPerPage,
        pages: dataConversation.pages,
      });
      setContactData(dataConversation.data);
    } catch (error) {
      console.error('Erro ao buscar dados de erros:', error);
    } finally {
      setIsLoadingContacts(false);
    }
  }, [dateFilter.startDate, dateFilter.endDate, tenantId, pageInfo.page, pageInfo.itemsPerPage]);

  useEffect(() => {
    if (dateFilter.startDate && dateFilter.endDate && tenantId) {
      fetchLineChart();
    }
  }, [fetchLineChart, dateFilter.startDate, dateFilter.endDate, tenantId]);

  return (
    <>
      <bds-grid direction="column" gap="2">
        <bds-grid direction="column" gap="1">
          {isLoadingContacts && contactData === null && 
            Array.from({ length: pageInfo.itemsPerPage }).map((_, index) => (
              <ContactCard
                key={`loading-${index}`}
                id=""
                loading={isLoadingContacts}
              />
            ))
          }
          {!isLoadingContacts && contactData && contactData.length === 0 && (
            <bds-grid justify-content="center" align-items="center" style={{ padding: '2rem', minHeight: '200px' }}>
              <div style={{ textAlign: 'center', color: '#666' }}>
                <div>Nenhum erro encontrado para o período selecionado</div>
              </div>
            </bds-grid>
          )}
          {contactData && contactData.length > 0 &&
            contactData.map((contact) => {
              const { cleanBotId, formattedUserId } = formatLinkParameters(contact.botId, contact.userId);

              return(
                <div key={contact.id} style={{ cursor: 'pointer' }}>
                  <a href={`https://${tenantId}.blip.ai/application/detail/${cleanBotId}/users/${formattedUserId}`} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none', color: 'inherit' }}>
                    <ContactCard
                      id={contact.userId}
                      loading={isLoadingContacts}
                      date={contact.date}
                      type="Erros"
                      data={{
                        type: contact.type,
                      }}
                    />
                  </a>
                </div>
              )})}
        </bds-grid>
        <bds-grid align-items="flex-end" justify-content="flex-end">
          {contactData && contactData.length > 0 &&
            <BdsPagination
              number-items={pageInfo.total}
              pages={pageInfo.pages}
              page-counter="false"
              onBdsPaginationChange={handlePaginationChange}
            />
          }
        </bds-grid>
      </bds-grid>
    </>
  );
};

export const ErrosSection = ({ className }: { className?: string }) => {
  const { dateFilter } = useDatamind();
  const { tenantId } = useAppContext();
  const [dataGraphic, setDataGraphic] = useState<BarChartData | null>(null);
  const [isLoadingChart, setIsLoadingChart] = useState(false);

  console.log('ErrosSection tenantId:', tenantId);

  useEffect(() => {
    const fetchLineChart = async () => {
      if (!dateFilter.startDate || !dateFilter.endDate || !tenantId) {
        return;
      }

      setIsLoadingChart(true);
      try {
        const dataChart = await dataAnalyticsService.getChartData({
          dataInicial: dateFilter.startDate,
          dataFinal: dateFilter.endDate,
          tenantId: tenantId,
          typeEnrichment: 'datamind_erro',
        });

        setDataGraphic(dataChart.graphic);
      } catch (error) {
        console.error('Erro ao buscar dados do gráfico de erros:', error);
      } finally {
        setIsLoadingChart(false);
      }
    };
  
    if (dateFilter.startDate && dateFilter.endDate && tenantId) {
      fetchLineChart();
    }
  }, [dateFilter.startDate, dateFilter.endDate, tenantId]);

  // Verifica se todos os valores nos datasets são 0 ou se não há dados
  const hasValidData = (data: BarChartData | null): boolean => {
    if (!data || !data.datasets) return false;
    
    return data.datasets.some(dataset => 
      dataset.data && dataset.data.some(value => value > 0)
    );
  };

  return (
    <>
      <bds-paper class={className}>
        <bds-grid gap="2" padding="2" direction="column" flexWrap="wrap">
          <HeaderCard
            title="Erros no período"
            subtitle="Indicadores sobre o erros das conversas no período selecionado"
          />
          {isLoadingChart ? (
            <bds-grid direction="row" justify-content="center" align-items="center" style={{ height: '350px' }}>
              <bds-loading-spinner />
            </bds-grid>
          ) : !dataGraphic || !hasValidData(dataGraphic) ? (
            <bds-grid direction="row" justify-content="center" align-items="center" style={{ height: '350px' }}>
              <div style={{ textAlign: 'center', color: '#666' }}>
                <div>Nenhum erro encontrado para o período selecionado</div>
              </div>
            </bds-grid>
          ) : (
            <bds-grid direction="row" md="auto" gap="1" flexWrap="wrap">
              <bds-grid direction="column" gap="1" md="6" padding="l-none" class="chart-container">
                <ChartContainer>
                  <BarChart data={dataGraphic} height={345} Legend={false} />
                </ChartContainer>
              </bds-grid>
              <bds-grid direction="column" gap="1" md="6" padding="r-none">
                <ErrosUserList />
              </bds-grid>
            </bds-grid>
          )}
        </bds-grid>
      </bds-paper>
    </>
  );
};
