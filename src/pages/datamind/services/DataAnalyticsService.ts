import { LineChartData } from '../components/charts';

export interface DataAnalyticsSummaryResponse {
  totalConversas: number;
  satisfacao: number;
  erros: number;
}

export interface ChartResponse {
  graphic: LineChartData;
}

export interface DataAnalyticsRequest {
  dataInicial: string;
  dataFinal: string;
  tenantId: string;
  typeEnrichment?: string;
  page?: number;
  itemsPerPage?: number;
}

export class DataAnalyticsService {
  private baseUrl: string;

  private convertDateFormat(date: string): string {
    const [day, month, year] = date.split('/');
    return `${year}/${month.padStart(2, '0')}/${day.padStart(2, '0')}`;
  }

  constructor() {
    this.baseUrl = 'https://data-mind.cs.blip.ai/';
  }

  async getSummaryCounts(request: DataAnalyticsRequest): Promise<DataAnalyticsSummaryResponse> {
    const dataInicialFormatted = this.convertDateFormat(request.dataInicial);
    const dataFinalFormatted = this.convertDateFormat(request.dataFinal);

    const url = `${this.baseUrl}api/DataAnalytics/summary/counts?dataInicial=${dataInicialFormatted}&dataFinal=${dataFinalFormatted}&tenantId=${request.tenantId}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          accept: '*/*',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return {
        totalConversas: data.total || 0,
        satisfacao: data.sentiment || 0,
        erros: data.erro || 0,
      };
    } catch (error) {
      console.error('Erro ao buscar dados do DataAnalytics/summary/counts:', error);
      throw error;
    }
  }

  async getChartData(request: DataAnalyticsRequest): Promise<ChartResponse> {
    const dataInicialFormatted = this.convertDateFormat(request.dataInicial);
    const dataFinalFormatted = this.convertDateFormat(request.dataFinal);

    const url = `${this.baseUrl}api/DataAnalytics/conversations/sentiment-graphic?dataInicial=${dataInicialFormatted}&dataFinal=${dataFinalFormatted}&tenantId=${request.tenantId}&typeEnrichment=${request.typeEnrichment}`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          accept: '*/*',
        },
        body: JSON.stringify({
          // TODO adjust this endpoint, this is a workaround
          // The API expects a POST request but the body is not used, it just reads the parameters in the URL
          // This is a workaround until the API is fixed
          dataInicial: dataInicialFormatted,
          dataFinal: dataFinalFormatted,
          tenantId: request.tenantId,
          typeEnrichment: request.typeEnrichment || 'datamind_sentimento',
          page: request.page || 1,
          itemsPerPage: request.itemsPerPage || 10,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return data;
    } catch (error) {
      console.error('Erro ao buscar dados do DataAnalytics/graphic:', error);
      throw error;
    }
  }

  async getConversationData(request: DataAnalyticsRequest): Promise<any> {
    const dataInicialFormatted = this.convertDateFormat(request.dataInicial);
    const dataFinalFormatted = this.convertDateFormat(request.dataFinal);

    const enrichment = request.typeEnrichment == 'datamind_sentimento' ? 'sentiment' : 'errors';

    const url = `${this.baseUrl}api/DataAnalytics/conversations/${enrichment}?dataInicial=${dataInicialFormatted}&dataFinal=${dataFinalFormatted}&tenantId=${request.tenantId}&page=${request.page}&itemsPerPage=${request.itemsPerPage}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          accept: '*/*',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return data;
    } catch (error) {
      console.error('Erro ao buscar dados do DataAnalytics/conversations:', error);
      throw error;
    }
  }
}

export const dataAnalyticsService = new DataAnalyticsService();
