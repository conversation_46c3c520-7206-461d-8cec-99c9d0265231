import React, { useEffect } from 'react';
import './datamind.scss';
import './components/DateFilter.scss';
import { BigNumberSection } from './sections/bignumber';
import { SentimentSection } from './sections/sentiment';
import { SidebarProvider, useSidebar } from './contexts/SidebarContext';
import { DatamindProvider, useDatamind } from './contexts/DatamindContext';
import { Sidebar } from './components/sidebar';
import { ErrosSection } from './sections/erros';
import { SuggestionsSection } from './sections/suggestions';
import { DateFilterComponent } from './components/DateFilter';
import { useAppContext } from '@src/contexts/AppContext';
import { BlipServiceProvider, useBlipService } from '@contexts/blip-service-context';

const DatamindContent: React.FC = () => {
  const { isOpen, title, content, closeSidebar } = useSidebar();
  const { loading, error } = useDatamind();
  const { user, tenantId } = useAppContext();
  const { msgingInstance } = useBlipService();

  useEffect(() => {
    const callTestCommand = async () => {
      // This effect is for testing purposes, remove or adjust as needed
      const testCommand = {
        method: 'get',
        uri: '/threads-merged/5521991509212%40wa.gw.msging.net?$take=20&direction=desc&refreshExpiredMedia=true', // Example URI, adjust as needed
        to: '<EMAIL>',
        id: crypto.randomUUID(), // Generate unique ID
        from: '<EMAIL>', // replace with bot identifier
        pp: `${user.email}/blip.ai/blip-insights-mfe-datamind`, // replace to blipService.getLocalNode()
        metadata: {
          'blip_portal.email': user.email,
          'server.shouldStore': 'true',
        },
      };
      const response = await msgingInstance?.processCommand(testCommand, 10000);
      console.log('Response from test command:', response); // eslint-disable-line no-console
    };
    callTestCommand();
  }, [msgingInstance, tenantId, user.email]);

  return (
    <>
      <bds-grid gap="2" padding="t-2" containerFluid direction="column" flexWrap="wrap">
        {loading && <div>Carregando dados...</div>}
        {error && <div style={{ color: 'red', padding: '1rem' }}>Erro: {error}</div>}

        <bds-grid>
          <DateFilterComponent className="datamind-date-filter" />
        </bds-grid>

        <BigNumberSection />
        <SentimentSection />
        <ErrosSection />
        <SuggestionsSection />
      </bds-grid>

      <Sidebar isOpen={isOpen} onClose={closeSidebar} title={title}>
        {content}
      </Sidebar>
    </>
  );
};

const Datamind: React.FC = () => {
  const { authtoken, settings } = useAppContext();
  return (
    <BlipServiceProvider
      webSocketParams={{
        blipDomainUrl: settings.blipDomainUrl || 'http://localhost:8080',
        blipWebsocketHostName: settings.blipWebsocketHostName || 'hmg-ws.blip.ai',
        blipWebsocketHostNameTenant: settings.blipWebsocketHostNameTenant || 'hmg-ws.blip.ai',
      }}
      blipServiceConfig={{
        blipDomain: settings.blipDomain || 'hmg.blip.ai',
        blipWebsocketPort: settings.blipWebsocketPort || '443',
        blipWebsocketScheme: settings.blipWebsocketScheme || 'wss',
        authToken: authtoken,
      }}
      applicationName="blip-insights-mfe-datamind"
    >
      <DatamindProvider>
        <SidebarProvider>
          <DatamindContent />
        </SidebarProvider>
      </DatamindProvider>
    </BlipServiceProvider>
  );
};

export default Datamind;
