# BlipService Implementation Guide

## Overview
This guide covers the BlipService implementation in the blip-insights-mfe project, providing developers with information on how to configure, use, and extend the service for messaging and thread operations.

## Architecture

### Core Components

#### BlipServiceContext (`BlipServiceContext.tsx`)
The primary React context that provides:
- **Connection Management**: Handles connection state, connection attempts, and error states
- **Singleton Integration**: Uses `BlipServiceSingleton` to ensure single connection instance
- **State Management**: Tracks `connected`, `isConnecting`, and `connectionError` states
- **Auto-reconnection**: Manages connection lifecycle with proper validation

#### BlipServiceSingleton (`BlipServiceSingleton.ts`)
A singleton pattern implementation that:
- **Prevents Multiple Connections**: Ensures only one BlipService instance exists
- **Promise-based Connection**: Uses promise containers for connection management
- **Configuration Handling**: Applies tenant-specific and environment configurations
- **Service Initialization**: Creates and connects the underlying BlipService instance

#### BlipService (from `blip-services` package)
The underlying service that provides:
- **WebSocket Communication**: Real-time messaging capabilities
- **Command Processing**: Execute commands against the Blip platform
- **Message Handling**: Send and receive messages
- **Authentication**: Handle user authentication and tokens

## Configuration

### Page-Level Configuration
The BlipService is configured at the page level rather than globally. Currently implemented in the Datamind page:

```tsx
// src/pages/datamind/index.tsx
const Datamind: React.FC = () => {
  const { authtoken } = useAppContext();

  return (
    <BlipServiceProvider
      webSocketParams={{
        blipDomainUrl: 'http://localhost:8080/', // ⚠️ Hardcoded - needs environment variable
        blipWebsocketHostName: 'hmg-ws.blip.ai', // ⚠️ Hardcoded - needs environment variable
        blipWebsocketHostNameTenant: 'hmg-ws.blip.ai', // ⚠️ Hardcoded - needs environment variable
      }}
      blipServiceConfig={{
        blipDomain: 'blip.ai',
        blipWebsocketPort: '443',
        blipWebsocketScheme: 'wss',
        authToken: authtoken,
      }}
      applicationName="blip-insights-mfe-datamind"
    >
      <DatamindProvider>
        <SidebarProvider>
          <DatamindContent />
        </SidebarProvider>
      </DatamindProvider>
    </BlipServiceProvider>
  );
};
```

### ⚠️ Configuration Issues
**Critical**: The following hardcoded values need to be replaced with environment variables:
- `blipDomainUrl`
- `blipWebsocketHostName`
- `blipWebsocketHostNameTenant`

## Usage

### Basic Connection
The BlipService automatically connects when the provider is mounted. Components can access the connection state:

```tsx
import { useBlipService } from '@contexts/blip-service-context';

const DatamindContent: React.FC = () => {
  const { msgingInstance, connected, isConnecting, connectionError } = useBlipService();
  const { user } = useAppContext();

  useEffect(() => {
    if (!msgingInstance) {
      console.log('BlipService not connected yet');
      return;
    }
    
    // Service is connected and ready to use
    console.log('BlipService connected successfully');
  }, [msgingInstance]);

  if (isConnecting) return <div>Connecting...</div>;
  if (connectionError) return <div>Error: {connectionError}</div>;
  if (!connected) return <div>Not connected</div>;

  return <div>Connected to BlipService</div>;
};
```

### Current Implementation Example
The Datamind page demonstrates how to use the BlipService for thread operations:

```tsx
useEffect(() => {
  const callTestCommand = async () => {
    if (!msgingInstance) {
      console.log('BlipService not connected yet');
      return;
    }

    const testCommand = {
      method: 'get',
      uri: '/threads-merged/5521991509212%40wa.gw.msging.net?$take=20&direction=desc&refreshExpiredMedia=true',
      to: '<EMAIL>',
      id: crypto.randomUUID(),
      from: '<EMAIL>',
      pp: `${user.email}/blip.ai/blip-insights-mfe-datamind`,
      metadata: {
        'blip_portal.email': user.email,
        'server.shouldStore': 'true',
      },
    };
    
    try {
      const response = await msgingInstance.processCommand(testCommand, 10000);
      console.log('Response from test command:', response);
    } catch (error) {
      console.error('Error calling test command:', error);
    }
  };

  callTestCommand();
}, [msgingInstance, user.email]);
```

## Provider Placement Strategy

### Page-Level Providers
The current implementation places the `BlipServiceProvider` at the page level, which provides several benefits:

1. **Lazy Loading**: WebSocket connections are only established when needed
2. **Isolated Connections**: Each page can have its own connection configuration
3. **Better Resource Management**: Connections are automatically closed when leaving the page
4. **Test Isolation**: Tests don't trigger unwanted WebSocket connections

### Provider Hierarchy
```tsx
<BlipServiceProvider>          // WebSocket connection management
  <DatamindProvider>           // Page-specific state management
    <SidebarProvider>          // UI state management
      <DatamindContent />      // Actual page content
    </SidebarProvider>
  </DatamindProvider>
</BlipServiceProvider>
```

## Testing

### Mocking BlipService
For unit tests, the BlipService is mocked to prevent actual WebSocket connections:

```tsx
// In test files
jest.mock('@contexts/blip-service-context', () => ({
  BlipServiceProvider: ({ children }: { children: React.ReactNode }) => children,
  useBlipService: () => ({
    msgingInstance: null,
    connected: false,
    isConnecting: false,
    connect: jest.fn(),
    connectionError: null,
  }),
}));
```

### Setup File Configuration
The `setupTests.js` file includes global mocks:

```javascript
// Mock WebSocket to prevent connection errors
global.WebSocket = class WebSocket {
  constructor() {
    this.readyState = 1; // OPEN
  }
  
  send() {}
  close() {}
  addEventListener() {}
  removeEventListener() {}
};

// Mock the entire blip-services package
jest.mock('blip-services', () => ({
  BlipService: class MockBlipService {
    // Mock implementation
  }
}));
```

## Development Notes

### Environment Variables Needed
Create environment-specific configurations for:

```typescript
// config/blipService.ts
export const getBlipServiceConfig = () => ({
  webSocketParams: {
    blipDomainUrl: process.env.REACT_APP_BLIP_DOMAIN_URL || 'http://localhost:8080/',
    blipWebsocketHostName: process.env.REACT_APP_BLIP_WEBSOCKET_HOST || 'hmg-ws.blip.ai',
    blipWebsocketHostNameTenant: process.env.REACT_APP_BLIP_WEBSOCKET_HOST_TENANT || 'hmg-ws.blip.ai',
  },
  blipServiceConfig: {
    blipDomain: process.env.REACT_APP_BLIP_DOMAIN || 'blip.ai',
    blipWebsocketPort: '443',
    blipWebsocketScheme: 'wss',
  },
  applicationName: 'blip-insights-mfe-datamind'
});
```

### Integration with AppContext
The service integrates with the existing AppContext to get authentication tokens:

```tsx
const { authtoken } = useAppContext(); // Retrieved from app-wide context
// Used in BlipServiceProvider configuration
```

### Next Steps
1. **Move provider to App.tsx**: since datamind is under development, to avoid opening unecessary connections the provider was moved to DatamindPage and now is openiing multiple connections, keeping it on App.tsx alongside another service-providers avoid the multiple connection issue
2. **Replace hardcoded configurations** with environment variables using the suggested config structure
3. **Add comprehensive error handling** for production use
4. **Create environment-specific configurations** for different deployment stages (dev, staging, prod)
5. **Add logging and monitoring** for WebSocket connection health
6. **Consider adding connection retry logic** for improved reliability
7. **Document command patterns** for different use cases beyond thread fetching
