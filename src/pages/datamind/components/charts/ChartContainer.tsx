import React from 'react';
import './ChartContainer.scss';

interface ChartContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  config?: any; // Para compatibilidade futura com configurações avançadas
}

export const ChartContainer: React.FC<ChartContainerProps> = ({
  children,
  title,
  description,
  className = '',
  config,
}) => {
  return (
    <>
      {(title || description) && (
        <div className="chart-container__header">
          {title && <h4 className="chart-container__title">{title}</h4>}
          {description && <p className="chart-container__description">{description}</p>}
        </div>
      )}
      <div className="chart-container__content">{children}</div>
    </>
  );
};

export default ChartContainer;
