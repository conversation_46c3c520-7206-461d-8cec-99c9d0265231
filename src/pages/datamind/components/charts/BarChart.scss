@import '~blip-ds/dist/collection/styles/_colors.scss';

:root {
  --bar-chart-color-1: #{$color-extended-blue};
  --bar-chart-color-1-border: #{$color-extended-blue-bright};
  --bar-chart-color-2: #{$color-extended-ocean};
  --bar-chart-color-2-border: #{$color-extended-ocean-bright};
  --bar-chart-color-3: #{$color-extended-green};
  --bar-chart-color-3-border: #{$color-extended-green-bright};
  --bar-chart-color-4: #{$color-extended-yellow};
  --bar-chart-color-4-border: #{$color-extended-yellow-bright};
  --bar-chart-color-5: #{$color-extended-orange};
  --bar-chart-color-5-border: #{$color-extended-orange-bright};
  --bar-chart-color-6: #{$color-extended-red};
  --bar-chart-color-6-border: #{$color-extended-red-bright};
  --bar-chart-color-7: #{$color-extended-pink};
  --bar-chart-color-7-border: #{$color-extended-pink-bright};
  --bar-chart-color-8: #{$color-extended-gray};
  --bar-chart-color-8-border: #{$color-extended-gray-bright};
}

.bar-chart {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .bar-chart-canvas {
    flex: 1;
    min-height: 0; // Importante para que o canvas possa encolher
    position: relative;

    canvas {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.bar-chart-blue {
  --primary-color: var(--bar-chart-color-1);
}

.bar-chart-red {
  --primary-color: var(--bar-chart-color-2);
}

.bar-chart-green {
  --primary-color: var(--bar-chart-color-3);
}

.bar-chart-yellow {
  --primary-color: var(--bar-chart-color-4);
}

.bar-chart-purple {
  --primary-color: var(--bar-chart-color-5);
}

.bar-chart-orange {
  --primary-color: var(--bar-chart-color-6);
}

.bar-chart-cyan {
  --primary-color: var(--bar-chart-color-7);
}

.bar-chart-pink {
  --primary-color: var(--bar-chart-color-8);
}
