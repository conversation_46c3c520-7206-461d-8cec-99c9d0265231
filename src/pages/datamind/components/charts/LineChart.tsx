import React, { useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { CustomLegend } from './CustomLegend';
import './LineChart.scss';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

ChartJS.defaults.font = {
  family: "'Nunito Sans', sans-serif",
  size: 12,
  weight: 'normal',
  style: 'normal',
};

export interface LineChartDataset {
  id: string;
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
  borderWidth?: number;
  borderDash?: number[];
  tension?: number;
  fill?: boolean;
  pointRadius?: number;
  pointHoverRadius?: number;
  pointBackgroundColor?: string;
  pointBorderColor?: string;
  pointBorderWidth?: number;
  pointHoverBorderWidth?: number;
  pointStyle?:
    | 'circle'
    | 'cross'
    | 'crossRot'
    | 'dash'
    | 'line'
    | 'rect'
    | 'rectRounded'
    | 'rectRot'
    | 'star'
    | 'triangle';
  pointHitRadius?: number;
}

export interface LineChartData {
  labels: string[];
  datasets: LineChartDataset[];
}

export interface LineChartProps {
  data: LineChartData;
  options?: ChartOptions<'line'>;
  className?: string;
  height?: number;
  width?: number;
  onLegendClick?: (datasetIndex: number, hidden: boolean) => void;
}

const defaultOptions: ChartOptions<'line'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          return `${tooltipItem.raw} ${tooltipItem.dataset.label.toLowerCase()}`;
        },
      },
    },
  },
  scales: {
    x: {
      border: {
        display: false,
      },
      grid: {
        display: false,
      },
      ticks: {
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
        color: '#636363',
      },
    },
    y: {
      border: {
        display: false,
      },
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)',
      },
      ticks: {
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
        color: '#636363',
      },
    },
  },
  elements: {
    line: {
      tension: 0,
    },
    point: {
      radius: 4,
      hoverRadius: 5,
      pointStyle: 'circle',
      borderWidth: 1,
      hitRadius: 10,
      hoverBorderWidth: 3,
    },
  },
};

const defaultColors = ['#80E3EB', '#B2DFFD', '#84EBBC', '#FDE99B', '#FCAA73', '#F99F9F', '#FD9BDC'];
const borderColors = ['#00C6D7', '#1968F0', '#35DE90', '#FBCF23', '#F06305', '#E60F0F', '#FB4BC1'];

export const LineChart: React.FC<LineChartProps> = ({
  data,
  options = {},
  className = '',
  height,
  width,
  onLegendClick = (datasetIndex: number, hidden: boolean) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Dataset ${datasetIndex} foi ${hidden ? 'ocultado' : 'exibido'}`);
    }
  },
}) => {
  const [hiddenDatasets, setHiddenDatasets] = useState<Set<number>>(new Set());

  const getColorFromId = (id: string): { backgroundColor: string; borderColor: string } | undefined => {
    const colorMap: Record<string, { backgroundColor: string; borderColor: string }> = {
      Bom: { backgroundColor: '#84EBBC', borderColor: '#35DE90' },
      Neutro: { backgroundColor: '#FDE99B', borderColor: '#FBCF23' },
      Ruim: { backgroundColor: '#F99F9F', borderColor: '#E60F0F' },
    };

    if (colorMap[id]) {
      return colorMap[id];
    }
    return undefined;
  };

  const handleToggleDataset = (index: number) => {
    const newHiddenDatasets = new Set(hiddenDatasets);
    const isHidden = newHiddenDatasets.has(index);

    if (isHidden) {
      newHiddenDatasets.delete(index);
    } else {
      newHiddenDatasets.add(index);
    }

    setHiddenDatasets(newHiddenDatasets);
    onLegendClick(index, !isHidden);
  };
  const limitedData = {
    ...data,
    labels: data.labels.slice(0, 8),
    datasets: data.datasets.map((dataset, index) => {
      const colors = getColorFromId(dataset.label);
      return {
        ...dataset,
        data: dataset.data.slice(0, 8),
        borderColor: dataset.borderColor || colors?.borderColor || borderColors[index % borderColors.length],
        backgroundColor:
          dataset.backgroundColor || colors?.backgroundColor || defaultColors[index % defaultColors.length],
        pointBackgroundColor:
          dataset.pointBackgroundColor || colors?.borderColor || borderColors[index % borderColors.length],
        pointHoverBorderWidth: dataset.pointHoverBorderWidth || 3,
        pointStyle: dataset.pointStyle || 'circle',
        pointHitRadius: dataset.pointHitRadius || 10,
        hidden: hiddenDatasets.has(index),
      };
    }),
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...options.plugins,
      legend: {
        ...defaultOptions.plugins?.legend,
        ...options.plugins?.legend,
        display: false,
      },
    },
    scales: {
      ...defaultOptions.scales,
      ...options.scales,
    },
  };

  return (
    <div className={`line-chart ${className}`} style={{ height, width }}>
      <div className="line-chart-canvas">
        <Line data={limitedData} options={mergedOptions} />
      </div>
      <CustomLegend
        datasets={data.datasets}
        hiddenDatasets={hiddenDatasets}
        onToggleDataset={handleToggleDataset}
        borderColors={data.datasets.map((dataset, index) => {
          const colors = getColorFromId(dataset.label);
          return colors?.borderColor || borderColors[index % borderColors.length];
        })}
      />
    </div>
  );
};

export default LineChart;
