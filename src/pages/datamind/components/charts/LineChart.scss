@import 'blip-ds/dist/collection/styles/_colors.scss';

:root {
  --line-chart-color-1: #{$color-extended-blue};
  --line-chart-color-1-border: #{$color-extended-blue-bright};
  --line-chart-color-2: #{$color-extended-ocean};
  --line-chart-color-2-border: #{$color-extended-ocean-bright};
  --line-chart-color-3: #{$color-extended-green};
  --line-chart-color-3-border: #{$color-extended-green-bright};
  --line-chart-color-4: #{$color-extended-yellow};
  --line-chart-color-4-border: #{$color-extended-yellow-bright};
  --line-chart-color-5: #{$color-extended-orange};
  --line-chart-color-5-border: #{$color-extended-orange-bright};
  --line-chart-color-6: #{$color-extended-red};
  --line-chart-color-6-border: #{$color-extended-red-bright};
  --line-chart-color-7: #{$color-extended-pink};
  --line-chart-color-7-border: #{$color-extended-pink-bright};
  --line-chart-color-8: #{$color-extended-gray};
  --line-chart-color-8-border: #{$color-extended-gray-bright};
}

.line-chart {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .line-chart-canvas {
    flex: 1;
    min-height: 0; // Importante para que o canvas possa encolher
    position: relative;

    canvas {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.line-chart-blue {
  --primary-color: var(--line-chart-color-1);
}

.line-chart-red {
  --primary-color: var(--line-chart-color-2);
}

.line-chart-green {
  --primary-color: var(--line-chart-color-3);
}

.line-chart-yellow {
  --primary-color: var(--line-chart-color-4);
}

.line-chart-purple {
  --primary-color: var(--line-chart-color-5);
}

.line-chart-orange {
  --primary-color: var(--line-chart-color-6);
}

.line-chart-cyan {
  --primary-color: var(--line-chart-color-7);
}

.line-chart-pink {
  --primary-color: var(--line-chart-color-8);
}
