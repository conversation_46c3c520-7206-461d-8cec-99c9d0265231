import React from 'react';
import { LineChartDataset } from './LineChart';
import './CustomLegend.scss';

interface CustomLegendProps {
  datasets: LineChartDataset[];
  hiddenDatasets: Set<number>;
  onToggleDataset: (index: number) => void;
  className?: string;
  borderColors?: string[];
}

const defaultBorderColors = ['#00C6D7', '#1968F0', '#35DE90', '#FBCF23', '#F06305', '#E60F0F', '#FB4BC1'];

export const CustomLegend: React.FC<CustomLegendProps> = ({
  datasets,
  hiddenDatasets,
  onToggleDataset,
  className = '',
  borderColors = defaultBorderColors,
}) => {
  return (
    <div className={`custom-legend ${className}`}>
      {datasets.map((dataset, index) => (
        <div
          key={index}
          className={`legend-item ${hiddenDatasets.has(index) ? 'hidden' : ''}`}
          onClick={() => onToggleDataset(index)}
        >
          <div
            className="legend-color"
            style={
              {
                backgroundColor: dataset.borderColor || borderColors[index % borderColors.length],
                '--legend-before-color': dataset.borderColor || borderColors[index % borderColors.length],
              } as React.CSSProperties
            }
          />
          <span className="legend-label">{dataset.label}</span>
        </div>
      ))}
    </div>
  );
};

export default CustomLegend;
