import React from 'react';
import './CustomTooltip.scss';

interface TooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  formatter?: (value: any, name: string, props: any) => [string | number, string];
  labelFormatter?: (label: string) => string;
  className?: string;
}

export const ChartTooltip: React.FC<TooltipProps> = ({
  active,
  payload,
  label,
  formatter,
  labelFormatter,
  className = '',
}) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className={`chart-tooltip ${className}`}>
      {label && <div className="chart-tooltip__label">{labelFormatter ? labelFormatter(label) : label}</div>}
      <div className="chart-tooltip__content">
        {payload.map((entry, index) => (
          <div key={index} className="chart-tooltip__item">
            <div className="chart-tooltip__indicator" style={{ backgroundColor: entry.color }} />
            <span className="chart-tooltip__name">{entry.name || entry.dataKey}:</span>
            <span className="chart-tooltip__value">
              {formatter ? formatter(entry.value, entry.name, entry)[0] : entry.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChartTooltip;
