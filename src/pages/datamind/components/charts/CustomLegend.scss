// Estilos para legenda personalizada
.custom-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  margin-top: 8px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    user-select: none;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.hidden {
      opacity: 0.5;

      .legend-color {
        background-color: #ccc !important;

        &::before {
          background-color: #ccc !important;
        }
      }

      .legend-label {
        text-decoration: line-through;
        color: #999;
      }
    }

    .legend-color {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      flex-shrink: 0;
      transition: background-color 0.2s ease;
      position: relative;

      &::before {
        content: '';
        width: 180%;
        height: 4px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 4px;
        transition: all 0.2s ease;
        background-color: var(--legend-before-color, #ce2727);
      }
    }

    .legend-label {
      font-family: '<PERSON><PERSON><PERSON>s', sans-serif;
      font-size: 12px;
      font-weight: 500;
      color: #333;
      transition: color 0.2s ease;
    }
  }

  // Variação para posicionamento horizontal
  &.horizontal {
    justify-content: flex-start;

    .legend-item {
      margin-right: 24px;
      margin-bottom: 8px;
    }
  }

  // Variação para posicionamento vertical
  &.vertical {
    flex-direction: column;
    align-items: flex-start;
    max-width: 200px;

    .legend-item {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

// Responsividade para telas menores
@media (max-width: 768px) {
  .custom-legend {
    gap: 12px;

    .legend-item {
      padding: 6px 10px;

      .legend-label {
        font-size: 11px;
      }
    }
  }
}
