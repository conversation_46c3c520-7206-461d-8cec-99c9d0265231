@import 'blip-ds/dist/collection/styles/_colors.scss';

.chart-tooltip {
  background: rgba(69, 69, 69, 0.95);
  border: 1px solid var(--color-border-light, #e6e6e6);
  border-radius: var(--border-radius-1, 8px);
  padding: var(--spacing-2, 16px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  z-index: 1000;

  &__label {
    color: var(--color-surface-1, #f6f6f6);
    font-family: 'Nunito Sans', sans-serif;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: var(--spacing-1, 8px);
    padding-bottom: var(--spacing-1, 8px);
    border-bottom: 1px solid var(--color-border-light, #e6e6e6);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 8px);
  }

  &__item {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 8px);
    font-family: 'Nunito Sans', sans-serif;
    font-size: 12px;
  }

  &__indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  &__name {
    color: var(--color-surface-1, #f6f6f6);
    font-weight: 400;
    flex-shrink: 0;
  }

  &__value {
    color: var(--color-surface-1, #f6f6f6);
    font-weight: 600;
    margin-left: auto;
  }
}
