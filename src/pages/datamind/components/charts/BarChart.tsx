import React, { useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { CustomLegend } from './CustomLegend';
import './BarChart.scss';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

ChartJS.defaults.font = {
  family: "'Nunito Sans', sans-serif",
  size: 12,
  weight: 'normal',
  style: 'normal',
};

export interface BarChartDataset {
  id: string;
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  borderSkipped?: boolean | 'bottom' | 'left' | 'right' | 'top' | 'start' | 'end' | 'middle';
}

export interface BarChartData {
  labels: string[];
  datasets: BarChartDataset[];
}

export interface BarChartProps {
  data: BarChartData;
  options?: ChartOptions<'bar'>;
  className?: string;
  height?: number;
  width?: number;
  Legend?: true | false;
  onLegendClick?: (datasetIndex: number, hidden: boolean) => void;
}

const defaultOptions: ChartOptions<'bar'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          return `${tooltipItem.raw} ${tooltipItem.dataset.label.toLowerCase()}`;
        },
      },
    },
  },
  scales: {
    x: {
      border: {
        display: false,
      },
      grid: {
        display: false,
      },
      ticks: {
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
        color: '#636363',
      },
    },
    y: {
      border: {
        display: false,
      },
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)',
      },
      ticks: {
        font: {
          family: "'Nunito Sans', sans-serif",
          size: 12,
        },
        color: '#636363',
      },
    },
  },
  elements: {
    bar: {
      borderWidth: 1,
    },
  },
};

const defaultColors = ['#80E3EB', '#B2DFFD', '#84EBBC', '#FDE99B', '#FCAA73', '#F99F9F', '#FD9BDC'];
const borderColors = ['#00C6D7', '#1968F0', '#35DE90', '#FBCF23', '#F06305', '#E60F0F', '#FB4BC1'];

export const BarChart: React.FC<BarChartProps> = ({
  data,
  options = {},
  className = '',
  height,
  width,
  Legend = true,
  onLegendClick = (datasetIndex: number, hidden: boolean) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Dataset ${datasetIndex} foi ${hidden ? 'ocultado' : 'exibido'}`);
    }
  },
}) => {
  const [hiddenDatasets, setHiddenDatasets] = useState<Set<number>>(new Set());

  const handleToggleDataset = (index: number) => {
    const newHiddenDatasets = new Set(hiddenDatasets);
    const isHidden = newHiddenDatasets.has(index);

    if (isHidden) {
      newHiddenDatasets.delete(index);
    } else {
      newHiddenDatasets.add(index);
    }

    setHiddenDatasets(newHiddenDatasets);
    onLegendClick(index, !isHidden);
  };

  const calculateYAxisConfig = (datasets: BarChartDataset[]) => {
    const allValues = datasets.flatMap((dataset) => dataset.data);
    const maxValue = Math.max(...allValues);

    let step: number;
    if (maxValue <= 10) step = 1;
    else if (maxValue <= 50) step = 5;
    else if (maxValue <= 100) step = 10;
    else if (maxValue <= 500) step = 50;
    else step = 100;

    const nextMultiple = Math.ceil(maxValue / step) * step;
    const suggestedMax = nextMultiple + 2 * step;

    return {
      suggestedMax,
      ticks: {
        stepSize: step,
      },
    };
  };
  const limitedData = {
    ...data,
    labels: data.labels.slice(0, 8),
    datasets: data.datasets.map((dataset: BarChartDataset, index: number) => {
      return {
        ...dataset,
        data: dataset.data.slice(0, 8),
        borderColor: dataset.borderColor || borderColors[index % borderColors.length],
        backgroundColor: dataset.backgroundColor || defaultColors[index % defaultColors.length],
        borderRadius: 8,
        hidden: hiddenDatasets.has(index),
      };
    }),
  };

  const yAxisConfig = calculateYAxisConfig(data.datasets);

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...options.plugins,
      legend: {
        ...defaultOptions.plugins?.legend,
        ...options.plugins?.legend,
        display: false,
      },
    },
    scales: {
      ...defaultOptions.scales,
      ...options.scales,
      y: {
        ...defaultOptions.scales?.y,
        ...options.scales?.y,
        ...yAxisConfig,
        ticks: {
          ...defaultOptions.scales?.y?.ticks,
          ...options.scales?.y?.ticks,
          ...yAxisConfig.ticks,
        },
      },
    },
  } as ChartOptions<'bar'>;

  return (
    <div className={`bar-chart ${className}`} style={{ height, width }}>
      <div className="bar-chart-canvas">
        <Bar data={limitedData} options={mergedOptions} />
      </div>
      {Legend && (
        <CustomLegend
          datasets={data.datasets}
          hiddenDatasets={hiddenDatasets}
          onToggleDataset={handleToggleDataset}
          borderColors={data.datasets.map((dataset: BarChartDataset, index: number) => {
            return borderColors[index % borderColors.length];
          })}
        />
      )}
    </div>
  );
};

export default BarChart;
