import React from 'react';
import { TooltipPostionType } from 'blip-ds/dist/types/components/tooltip/tooltip';
import './bigNumberCard.scss';

export interface BigNumberCardProps {
  value: string;
  title: string;
  tooltip: {
    text: string;
    position: TooltipPostionType;
  };
  icon: string;
  className?: string;
}

export const BigNumberCard = ({ value, title, tooltip, icon, className }: BigNumberCardProps) => {
  return (
    <bds-paper class={className}>
      <bds-grid direction="row" padding="2" gap="1" align-items="baseline">
        <bds-paper class="paper-icon bg-color-surface-2 rounded-2">
          <bds-grid padding="1">
            <bds-icon name={icon} theme="outline" size="medium"></bds-icon>
          </bds-grid>
        </bds-paper>
        <div className="big-number-card__content">
          <div className="big-number-card__value">{value}</div>
          <div className="big-number-card__label">
            {title}
            <bds-tooltip
              id="variation-tooltip"
              tooltip-text={tooltip.text}
              position={tooltip.position}
              class="big-number-card__tooltip"
            >
              <bds-icon name="info" size="x-small"></bds-icon>
            </bds-tooltip>
          </div>
        </div>
      </bds-grid>
    </bds-paper>
  );
};
