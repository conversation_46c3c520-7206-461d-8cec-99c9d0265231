import React from 'react';
import './sidebar.scss';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <>
      <div className="sidebar-overlay" onClick={onClose} />

      <div className={`sidebar ${isOpen ? 'sidebar--open' : ''}`}>
        <div className="sidebar__header">
          {title && (
            <bds-typo variant="fs-20" bold="bold" class="sidebar__title">
              {title}
            </bds-typo>
          )}
          <bds-button variant="ghost" size="short" icon="close" onClick={onClose} class="sidebar__close-button" />
        </div>

        <div className="sidebar__content">{children}</div>
      </div>
    </>
  );
};
