.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 999;
  backdrop-filter: blur(2px);
}

.sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--color-neutral-light-00, #ffffff);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: right 0.3s ease-in-out;
  display: flex;
  flex-direction: column;

  &--open {
    right: 0;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-neutral-light-20, #e0e0e0);
    min-height: 72px;
  }

  &__title {
    color: var(--color-neutral-dark-90, #333333);
    margin: 0;
  }

  &__close-button {
    flex-shrink: 0;
  }

  &__content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;

    /* Scrollbar personalizado */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: var(--color-neutral-light-10, #f5f5f5);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-neutral-light-40, #cccccc);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--color-neutral-light-50, #b3b3b3);
    }
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .sidebar {
    width: 90vw;
    right: -90vw;

    &--open {
      right: 0;
    }
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
    right: -100vw;

    &--open {
      right: 0;
    }
  }
}
