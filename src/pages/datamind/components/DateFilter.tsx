import React, { useEffect, useState, useRef, useCallback } from 'react';
import { DateFilter, useDatamind } from '../contexts/DatamindContext';
import { BdsChipClickable, BdsDatepicker } from 'blip-ds/dist/blip-ds-react/components';
import { formatToBrazilianDate } from '../../../utils/date';

interface DateFilterProps {
  className?: string;
}

export const DateFilterComponent: React.FC<DateFilterProps> = ({ className }) => {
  const { dateFilter, updateDateFilter } = useDatamind();
  const [selectedDays, setSelectedDays] = useState(7);

  const calculateDates = (days: number) => {
    const startDate = new Date('2025-05-12');
    const endDate = new Date('2025-05-12');
    endDate.setDate(endDate.getDate() + days);

    return {
      startDate: formatToBrazilianDate(startDate),
      endDate: formatToBrazilianDate(endDate),
    };
  };

  const concludedDate = (e: any) => {
    const { startDate, endDate } = e.detail;

    updateDateFilter({
      startDate: startDate,
      endDate: endDate,
    });
  }

  const handleChipClick = (days: number) => {
    setSelectedDays(days);
    const dates = calculateDates(days);

    updateDateFilter({
      startDate: dates.startDate,
      endDate: dates.endDate,
    });
  };

  useEffect(() => {
    if (!dateFilter.startDate && !dateFilter.endDate) {
      handleChipClick(7);
    }
  }, [dateFilter.startDate, dateFilter.endDate]); // eslint-disable-line react-hooks/exhaustive-deps


  const defaultDates = calculateDates(7);

  return (
    <bds-grid lg="auto" md="auto" sm="auto" xg="auto" xs="auto" xxs="auto" justify-content="space-between">
      <bds-grid gap="1" align-items="center">
        {[7, 15, 30].map((days) => (
          <BdsChipClickable
            key={days}
            clickable
            color={selectedDays === days ? 'default' : 'outline'}
            onClick={() => handleChipClick(days)}
          >
            {days} dias
          </BdsChipClickable>
        ))}
      </bds-grid>

      <bds-grid>
        <BdsDatepicker
          startDateLimit="12/05/2025"
          endDateLimit="12/06/2025"
          typeOfDate="period"
          positionOptions="bottom-center"
          valueDateSelected={dateFilter.startDate || defaultDates.startDate}
          valueEndDateSelected={dateFilter.endDate || defaultDates.endDate}
          onConcludeDatepicker={concludedDate}
        />
      </bds-grid>
    </bds-grid>
  );
};

export default DateFilterComponent;

