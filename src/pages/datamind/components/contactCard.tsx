import React, { useState } from 'react';
import './contactCard.scss';
import { BdsChipTag } from 'blip-ds/dist/blip-ds-react/components';

interface SentimentEnrichment {
  sentiment_status: 'Ruim' | 'Bom' | 'Neutro';
}

interface ErrorEnrichment {
  type: string[][];
}

interface ContactCardProps {
  id: string;
  loading: boolean;
  date?: string;
  type?: 'Sentiment' | 'Erros';
  data?: SentimentEnrichment | ErrorEnrichment;
}

const SENTIMENT_CONFIG = {
  Bom: {
    color: 'success',
    icon: 'emoji',
    text: 'Bom',
  },
  Neutro: {
    color: 'warning',
    icon: 'emoji',
    text: 'Neutro',
  },
  Ruim: {
    color: 'danger',
    icon: 'emoji-negative',
    text: 'Ruim',
  },
} as const;

const getSentimentProperties = (status: SentimentEnrichment['sentiment_status']) => {
  const config = SENTIMENT_CONFIG[status];

  if (!config) {
    console.warn('Sentiment status not found in config:', status, 'Available keys:', Object.keys(SENTIMENT_CONFIG));
    return {
      color: 'info' as const,
      icon: 'emoji',
      text: 'Indefinido',
    };
  }
  
  return config;
};

const ContactCardSentiment = (props: SentimentEnrichment) => {
  const { color, icon, text } = getSentimentProperties(props.sentiment_status);

  return (
    <bds-grid lg="auto" md="auto" sm="auto" xs="auto" direction="column" align-items="flex-end">
      <bds-chip-tag color={color} icon={icon}>
        {text}
      </bds-chip-tag>
    </bds-grid>
  );
};

const ContactCardError = (props: ErrorEnrichment) => {
  const getLastStringFromArrays = (data: string[][]) => {
    const maxVisibleItems = 2;
    const totalItems = data.length;
    
    if (totalItems <= maxVisibleItems) {
      return data.map((arr, index) => (
        <BdsChipTag color="outline" icon="" key={index}>{arr[arr.length - 1]}</BdsChipTag>
      ));
    }
    
    const visibleItems = data.slice(0, maxVisibleItems).map((arr, index) => (
      <BdsChipTag color="outline" icon="" key={index}>{arr[arr.length - 1]}</BdsChipTag>
    ));
    
    const remainingCount = totalItems - maxVisibleItems;
    const moreIndicator = (
      <BdsChipTag color="outline" icon="" key="more">+{remainingCount}</BdsChipTag>
    );
    
    return [...visibleItems, moreIndicator];
  };

  return (
    <bds-grid
      lg="auto"
      md="auto"
      sm="auto"
      xs="auto"
      direction="row"
      align-items="flex-end"
      justify-content="flex-end"
      gap="1"
    >
      {getLastStringFromArrays(props.type)}
    </bds-grid>
  );
};

export const ContactCard = ({ id, loading, date, type, data }: ContactCardProps) => {
  const [name, setName] = useState<string | undefined>(undefined);

  if(loading) {
    return (
      <bds-paper class="contact-card">
        <bds-grid direction="column" padding="2" gap="1">
          <bds-grid direction="row" align-items="center" gap="2">
            <bds-grid direction="column">
              <bds-skeleton shape="square" width="150px" height="14px"></bds-skeleton>
              <bds-skeleton shape="square" width="60px" height="14px"></bds-skeleton>
            </bds-grid>
          </bds-grid>
        </bds-grid>
      </bds-paper>
    )
  }

  return (
    <bds-paper class="contact-card">
      <bds-grid direction="column" padding="2" gap="1">
        <bds-grid direction="row" align-items="center" gap="2">
          <bds-grid gap="2">
            {/* <bds-avatar name={name} size="small" /> */}

            <bds-grid direction="column">
              <bds-typo variant="fs-12" tag="p" bold="bold">
                {name || id}
              </bds-typo>
              <bds-typo variant="fs-12" tag="p">
                Data da conversa: {date}
              </bds-typo>
            </bds-grid>
          </bds-grid>

          {type === 'Sentiment' && data && !Array.isArray(data) && (
            <ContactCardSentiment {...(data as SentimentEnrichment)} />
          )}
          {type === 'Erros' && data && !Array.isArray(data) && <ContactCardError {...(data as ErrorEnrichment)} />}
        </bds-grid>
      </bds-grid>
    </bds-paper>
  );
};
