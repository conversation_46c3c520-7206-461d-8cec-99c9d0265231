import React from 'react';

export const HeaderCard: React.FC<{
  title: string;
  subtitle?: string;
  className?: string;
}> = ({ title, subtitle, className }) => {
  return (
    <bds-grid direction="column" class={className}>
      <bds-typo tag="h4" bold="bold" variant="fs-20" margin={false}>
        {title}
      </bds-typo>
      {subtitle && (
        <bds-typo tag="p" variant="fs-14">
          {subtitle}
        </bds-typo>
      )}
    </bds-grid>
  );
};
