import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSearchParams } from 'react-router-dom';

export interface DateFilter {
  startDate: string;
  endDate: string;
}

export interface DatamindData {
  id: string;
  name: string;
  metrics: any[];
}

interface DatamindContextType {
  data: DatamindData | null;
  loading: boolean;
  error: string | null;

  urlParameter: string | null;

  dateFilter: DateFilter;

  updateDateFilter: (dates: DateFilter) => void;
}

const DatamindContext = createContext<DatamindContextType | undefined>(undefined);

interface DatamindProviderProps {
  children: ReactNode;
}

const getDefaultDateFilter = (): DateFilter => {
  return {
    startDate: '',
    endDate: '',
  };
};

export const DatamindProvider: React.FC<DatamindProviderProps> = ({ children }) => {
  const [searchParams] = useSearchParams();
  const [data, setData] = useState<DatamindData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilter>(getDefaultDateFilter());

  const urlParameter = searchParams.get('tenantId');

  const updateDateFilter = (dates: DateFilter) => {
    setDateFilter(dates);
  };

  const contextValue: DatamindContextType = {
    data,
    loading,
    error,
    urlParameter,
    dateFilter,
    updateDateFilter,
  };

  return <DatamindContext.Provider value={contextValue}>{children}</DatamindContext.Provider>;
};

export const useDatamind = (): DatamindContextType => {
  const context = useContext(DatamindContext);
  if (context === undefined) {
    throw new Error('useDatamind deve ser usado dentro de um DatamindProvider');
  }
  return context;
};

export default DatamindContext;
