import { WebSocketParams } from '@src/typings/WebSocketParams';

/**
 * Configuration for BlipService based on the datamind component requirements
 * This matches the configuration shown in the commented code
 */
export const getDatamindBlipServiceConfig = () => {
  // Configuration from the commented code in datamind/index.tsx
  const blipServiceConfig = {
    tenant: 'tenant-id', // Will be replaced with actual tenant
    blipDomainUrl: 'http://localhost:8080/', // For development
    blipDomain: 'msging.net',
    blipWebsocketPort: '443',
    blipWebsocketScheme: 'wss',
    blipWebsocketHostName: 'hmg-ws.msging.net',
    blipWebsocketHostNameTenant: 'hmg-ws.msging.net',
    applicationName: 'blip-insights-mfe-datamind'
  };

  const webSocketParams: WebSocketParams = {
    blipDomainUrl: blipServiceConfig.blipDomainUrl,
    blipWebsocketHostName: blipServiceConfig.blipWebsocketHostName,
    blipWebsocketHostNameTenant: blipServiceConfig.blipWebsocketHostNameTenant
  };

  return {
    webSocketParams,
    blipServiceConfig: {
      blipDomain: blipServiceConfig.blipDomain,
      blipWebsocketPort: blipServiceConfig.blipWebsocketPort,
      blipWebsocketScheme: blipServiceConfig.blipWebsocketScheme
    },
    applicationName: blipServiceConfig.applicationName
  };
};
