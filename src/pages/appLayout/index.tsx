import React, { useEffect, useState } from 'react';
import '@styles/pages/appLayout.scss';
import { Footer } from '@components/footer';
import { NavBar } from '@components/navBar';
import { Header } from '@components/header';
import { Container } from '@components/container';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { TabProvider } from '@contexts/TabsContext';
import { useAppContext } from '@contexts/AppContext';
import BlipChatWidget from '@components/blip_chat_widget';


const redirectToHome = () => {
  setTimeout(() => window.location.href = '/application', 8000);
}

export const AppLayout = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const { isCheckingUserAcess, userHasAccess } = useAppContext();
  const [ warningMessage, setWarningMessage ] = useState<string>(translations.messages.unauthorized);
  const [ showWarning, setShowWarning ] = useState<boolean>(false);
  const { tenantId } = useAppContext();

  useEffect(() => {
    if (tenantId === 'portal') {
      setWarningMessage(translations.messages.contractSelectionNeeded);
      setShowWarning(true);
      return redirectToHome();
    }
    if (!isCheckingUserAcess && !userHasAccess) {
      setWarningMessage(translations.messages.unauthorized);
      setShowWarning(true);
      redirectToHome();
    }
  }, [isCheckingUserAcess, userHasAccess, tenantId, translations]);

  return (
    isCheckingUserAcess || !userHasAccess? (
      <LoadingBody warningMessage={warningMessage} showToast={showWarning} />
    ) : (
      <AppBody tenantId={tenantId} />
    )
  );
};

const AppBody = ({ tenantId }: { tenantId: string }) => {
  return (
    <bds-grid class="app-layout" direction="column">
      <NavBar />
      <Header />
      <TabProvider>
        <Container />
      </TabProvider>
      <BlipChatWidget tenantId={tenantId} />
      <Footer />
    </bds-grid>
  );
};

const LoadingBody = ({warningMessage, showToast}: LoadingBodyProps) => {
  return (
    <bds-grid class="loading-layout" direction="column">
      <bds-loading-spinner size="standard" />
      <bds-toast class="loading-layout__toast" toast-text={warningMessage} show={showToast} variant="system" />
    </bds-grid>
  );
}

interface LoadingBodyProps {
  warningMessage: string;
  showToast: boolean;
}
