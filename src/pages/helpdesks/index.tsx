/* eslint-disable */ // TODO= remover depois de inserir as funções no onChange
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import BigNumberCard from '@components/big_number_card';
import Filters from '@components/filters';
import FilterSelect, { FilterSelectOption } from '@components/filter_select';
import { formatToBrazilianDate } from '@utils/date';
import { Insights } from '@components/insights';
import { BackendResponse } from '@services/BlipInsightsService';
import { useBlipInsightsServices } from '@hooks/useServices';
import './helpdesks.scss';
import {
  getInsightsDataQuery,
  mapInsightsToDatabricksInsigthsRequestData,
} from '@services/query/HelpdeskQueryBuilder';
import { formatBigNumbersData } from '@utils/helpdesk/BigNumbersHelper';
import {
  finishedFetchLastDataUpdateEventName,
  finishedOnApplyFilterEventName,
  shallRefreshChartsEventName,
  startedFetchLastDataUpdateEventName,
  startedOnApplyFilterEventName,
} from '@utils/constants';
import { useAppContext } from '@contexts/AppContext';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import { TableTicketsPanel } from '@components/tables/tickets_panel';
import PerformanceGraphHelpdesks from '@components/performance_graph_helpdesks';
import { TicketsVolumeHeatMap } from '@components/heat_map/tickets_volume';
import { ServiceRegistry } from '@services/registry/serviceRegistry';
import { BigNumbersHelpdesksService } from '@services/helpdesks/BigNumbersHelpdesksService';
import { AllChartsDataInterface } from '@typings/AllChartsDataInterface';
import { PerformanceGraphHelpdesksService } from '@services/helpdesks/PerformanceGraphHelpdesksService';
import { TicketsHelpdesksService } from '@services/helpdesks/TicketsHelpdesksService';
import { HeatmapHelpdesksService } from '@services/helpdesks/HeatmapHelpdesksService';
import { responseKeys, serviceKeys } from '@services/helpdesks/ServiceRegistryUtils';
import { FiltersLoaderService } from '@services/filters/FiltersLoaderService';
import { toInsightsFilter } from '@utils/filters';

const setDataForFilter = (
  response: BackendResponse,
  setFunction: ((channels: FilterSelectOption[]) => void) | undefined
) => {
  if (response?.results?.length) {
    const filterItemsFromResponse = response.results.map((item: any) => {
      return { label: item[0], value: item[0] };
    });
    if (setFunction) {
      setFunction(filterItemsFromResponse);
    }
  }
};

export const Helpdesks: React.FC = () => {
  const { blipInsightsService, apiBlipInsightsService } = useBlipInsightsServices();
  const { tenantId } = useAppContext();
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [selectedFilter, setSelectedFilter] = useState<HelpdeskFilter>({
    startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
    endDate: new Date(),
  } as HelpdeskFilter);
  const [isFiltersDataLoaded, setIsFiltersDataLoaded] = useState(false);
  const [helpdeskInsights, setHelpdeskInsights] = useState<string>('');
  const [isInsightsLoading, setIsInsightsLoading] = useState(false);
  const [applyButtonDisabled, setApplyButtonDisabled] = useState(true);
  const [clearButtonDisabled, setClearButtonDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [columnsBigNumbers, setColumnsBigNumbers] = useState(4);
  const serviceRegistry = useRef(new ServiceRegistry(apiBlipInsightsService, tenantId));
  const filtersLoaderService = useRef(new FiltersLoaderService());

  const [channels, setChannels] = useState<FilterSelectOption[]>([]);
  const [routerIds, setRouterIds] = useState<FilterSelectOption[]>([]);
  const [botIds, setBotIds] = useState<FilterSelectOption[]>([]);
  const [queues, setQueues] = useState<FilterSelectOption[]>([]);
  const [allChartsData, setAllChartsData] = useState<AllChartsDataInterface>({});

  const startedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  const finishedFetchLastDateUpdate = useCallback(() => {
    setApplyButtonDisabled(false);
    setClearButtonDisabled(false);
  }, [setApplyButtonDisabled, setClearButtonDisabled]);

  useEffect(() => {
    const updateColumnsBigNumbers = () => {
      setColumnsBigNumbers(window.innerWidth <= 1366 ? 3 : 4);
    };

    updateColumnsBigNumbers();
    window.addEventListener('resize', updateColumnsBigNumbers);

    return () => {
      window.removeEventListener('resize', updateColumnsBigNumbers);
    };
  }, []);

  useEffect(() => {
    return () => {
      // Code to execute on component destroy
      document.removeEventListener(shallRefreshChartsEventName, onApplyFilter);
      document.removeEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
      document.removeEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);
    };
  }, []);

  useEffect(() => {
    document.addEventListener(shallRefreshChartsEventName, onApplyFilter);
    document.addEventListener(startedFetchLastDataUpdateEventName, startedFetchLastDateUpdate);
    document.addEventListener(finishedFetchLastDataUpdateEventName, finishedFetchLastDateUpdate);

    if (channels?.length && routerIds?.length && botIds?.length && queues?.length) {
      setIsFiltersDataLoaded(true);
      return;
    }

    fetchFiltersData();
  }, [blipInsightsService]);

  useEffect(() => {
    if (!isFiltersDataLoaded) {
      return;
    }
    const applyFilterAsync = async () => {
      onApplyFilter();
    };
    // applyFilterAsync is async to prevent layout shift
    applyFilterAsync();
  }, [isFiltersDataLoaded]);

  useEffect(() => {
    serviceRegistry.current.clearRegistry();
    const isPreviousPeriodQuery = true;
    serviceRegistry.current.addServiceToRegistry(serviceKeys.BigNumbersHelpdesksService, new BigNumbersHelpdesksService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.BigNumbersHelpdesksServicePreviousPeriod, new BigNumbersHelpdesksService(isPreviousPeriodQuery));
    serviceRegistry.current.addServiceToRegistry(serviceKeys.PerformanceGraphHelpdesksService, new PerformanceGraphHelpdesksService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.TicketsHelpdesksService, new TicketsHelpdesksService());
    serviceRegistry.current.addServiceToRegistry(serviceKeys.HeatmapHelpdesksService, new HeatmapHelpdesksService());
  }, [serviceRegistry, blipInsightsService, tenantId]);

  const getDataFromService = useCallback((serviceKey: string, responseKey: string) => {
    const service = serviceRegistry.current.getService(serviceKey);
    if (!service) return null;
    const response = allChartsData[responseKey];
    return service.getFormattedData(response);
  }, [allChartsData, serviceRegistry]);
  
  const bigNumbersData = useMemo(() => {
    const currentData = getDataFromService(serviceKeys.BigNumbersHelpdesksService, responseKeys.BigNumbersHelpdesksServiceResponse) || [];
    const previousData = getDataFromService(serviceKeys.BigNumbersHelpdesksServicePreviousPeriod, responseKeys.BigNumbersHelpdesksServicePreviousPeriodResponse) || [];
    return formatBigNumbersData(currentData, previousData);
  }, [getDataFromService]);

  const performanceGraphData = useMemo(() => {
    return getDataFromService(serviceKeys.PerformanceGraphHelpdesksService, responseKeys.PerformanceGraphHelpdesksServiceResponse);
  }, [getDataFromService]);

  const ticketsPanelQueuesData = useMemo(() => {
    return getDataFromService(serviceKeys.TicketsHelpdesksService, responseKeys.TicketsHelpdesksServiceResponse) || [];
  }, [getDataFromService]);

  const ticketsHeatMapData = useMemo(() => {
    return getDataFromService(serviceKeys.HeatmapHelpdesksService, responseKeys.HeatmapHelpdesksServiceResponse);
  }, [getDataFromService]);

  const fetchHelpdeskInsights = async () => {
    setIsInsightsLoading(true);
    try { 
      const baseFilter = toInsightsFilter('insights_data', selectedFilter, tenantId);
      const insightsRequestData = await apiBlipInsightsService.getData(baseFilter);
      const insightsData =
        (insightsRequestData?.results &&
          insightsRequestData?.results.filter((i: any) => i !== null)) ||
        [];
      const res = await blipInsightsService.getInsightsData(
        mapInsightsToDatabricksInsigthsRequestData(insightsData, tenantId, selectedFilter)
      );
      setHelpdeskInsights(res.predictions.insight_llm);
    } catch (error) {
    } finally {
      setIsInsightsLoading(false);
    }
  };

  const fetchFiltersData = async () => {
    const [
      fetchedChannels,
      fetchedRouters,
      fetchedBots,
      fetchedQueues
    ] = await filtersLoaderService.current.getAllHelpdesksFiltersData(tenantId, apiBlipInsightsService);

    if (!channels?.length && fetchedChannels) {
      setDataForFilter(fetchedChannels, setChannels);
    }
    if (!routerIds?.length && fetchedRouters) {
      setDataForFilter(fetchedRouters, setRouterIds);
    }
    if (!botIds?.length && fetchedBots) {
      setDataForFilter(fetchedBots, setBotIds);
    }
    if (!queues?.length && fetchedQueues) {
      setDataForFilter(fetchedQueues, setQueues);
    }

    setIsFiltersDataLoaded(true);
  };

  const getSelectedValues = (item: CustomEvent) => {
    return Object.keys(item.detail.value)
      .map((e) => item.detail.value[e])
      .filter((e) => e.checked)
      .map((e) => e.value);
  };

  const onChangeChannels = (item: CustomEvent) => {
    selectedFilter.channels = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeRouters = (item: CustomEvent) => {
    selectedFilter.routerIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeBots = (item: CustomEvent) => {
    selectedFilter.botIds = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeQueues = (item: CustomEvent) => {
    selectedFilter.queues = getSelectedValues(item);
    setSelectedFilter(selectedFilter);
  };

  const onChangeStartDate = (item: CustomEvent) => {
    selectedFilter.startDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onChangeEndDate = (item: CustomEvent) => {
    selectedFilter.endDate = item.detail.value;
    setSelectedFilter(selectedFilter);
  };

  const onApplyFilter = async () => {
    document.dispatchEvent(new CustomEvent(startedOnApplyFilterEventName));
    setIsLoading(true);
    setApplyButtonDisabled(true);
    setClearButtonDisabled(true);
    try {
      const allChartsDataResponse = await serviceRegistry.current.loadAllDataFromRegisteredServices(selectedFilter);
      setAllChartsData(allChartsDataResponse);
      fetchHelpdeskInsights();
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
      setApplyButtonDisabled(false);
      setClearButtonDisabled(false);
      document.dispatchEvent(new CustomEvent(finishedOnApplyFilterEventName));
    }
  };

  const clearFilter = () => {
    const defaultFilter = {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      channels: [],
      routerIds: [],
      botIds: [],
      queues: [],
    } as HelpdeskFilter;

    setSelectedFilter({ ...defaultFilter });

    const datepicker = document.querySelector('bds-datepicker');
    if (datepicker) {
      datepicker.valueDateSelected = undefined;
      datepicker.valueEndDateSelected = undefined;

      setTimeout(() => {
        datepicker.valueDateSelected = formatToBrazilianDate(defaultFilter.startDate);
        datepicker.valueEndDateSelected = formatToBrazilianDate(defaultFilter.endDate);
      }, 0);
    }

    document.querySelectorAll('bds-autocomplete').forEach((autocomplete) => {
      const shadowRoot = autocomplete.shadowRoot;
      if (shadowRoot) {
        const selectOptions = shadowRoot.querySelectorAll('bds-select-option');
        selectOptions.forEach((option) => {
          if (option.checked === true) {
            option.checked = false;
            option.dispatchEvent(new CustomEvent('optionChecked', { detail: { checked: false } }));
          }
        });
      }
    });
  };

  return (
    <bds-grid class="helpdesks">
      <div className="helpdesks__filter-container">
        <Filters
          onApplyFilter={onApplyFilter}
          clearFilter={clearFilter}
          startDate={formatToBrazilianDate(new Date(new Date().setDate(new Date().getDate() - 7)))}
          endDate={formatToBrazilianDate(new Date())}
          onStartDateChange={onChangeStartDate}
          onEndDateChange={onChangeEndDate}
          applyButtonDisabled={applyButtonDisabled}
          clearButtonDisabled={clearButtonDisabled}
        >
          <FilterSelect
            options={channels || []}
            label="Canal"
            onChange={onChangeChannels}
            placeholder="Todos"
            value={selectedFilter.channels}
          />
          <FilterSelect
            options={routerIds || []}
            label="ID do roteador"
            onChange={onChangeRouters}
            placeholder="Todos"
            value={selectedFilter.routerIds}
          />
          <FilterSelect
            options={botIds || []}
            label="ID do chatbot"
            onChange={onChangeBots}
            placeholder="Todos"
            value={selectedFilter.botIds}
          />
          <FilterSelect
            options={queues || []}
            label="Fila"
            onChange={onChangeQueues}
            placeholder="Todos"
            value={selectedFilter.queues}
          />
        </Filters>
      </div>
      {isLoading && (
        <div className="helpdesks__loading-overlay">
          <bds-icon name="loading" size="medium"></bds-icon>
          <bds-typo> Carregando os dados...</bds-typo>
        </div>
      )}

      {!isLoading && (
        <>
          <div className="helpdesks__big-numbers-grid">
            {bigNumbersData?.length === 0 && (
              <div className="helpdesks__no-data-message">
                <bds-typo variant="fs-16" bold="bold">
                  Não há dados para o filtro selecionado
                </bds-typo>
              </div>
            )}
            {bigNumbersData.map((item, index) => (
              <BigNumberCard
                key={index}
                type="period"
                title={item.title}
                variation={item.variation}
                icon={item.icon}
                tooltip={{
                  text: item.tooltip.text,
                  position: (index + 1) % columnsBigNumbers === 0 ? 'bottom-right' : item.tooltip.position,
                }}
                value={item.value}
                chipColor={item.chipColor}
              />
            ))}
          </div>
          <div className="helpdesks__performance-graph-container">
            <PerformanceGraphHelpdesks
              labelPeriods={performanceGraphData.labelPeriods}
              selectOptions={performanceGraphData.selectOptions}
              openTicketsData={performanceGraphData.openTicketsData}
              closedTicketsData={performanceGraphData.closedTicketsData}
              avarageTime={performanceGraphData.avarageTime}
            />
             {serviceRegistry.current
              .getService(serviceKeys.PerformanceGraphHelpdesksService)
              ?.getFormattedData(allChartsData[responseKeys.PerformanceGraphHelpdesksServiceResponse])?.openTicketsData?.length >
              0 &&(helpdeskInsights.length || isInsightsLoading) && (
              <Insights textInsight={helpdeskInsights} loading={isInsightsLoading}></Insights>
            )}
          </div>
          <div className="helpdesks__table-tickets-panel-container">
            <TableTicketsPanel data={ticketsPanelQueuesData} tenantId={tenantId} selectedFilter={selectedFilter} />
          </div>
          <div className="helpdesks__table-tickets-heat-map">
            <TicketsVolumeHeatMap
              openingData={ticketsHeatMapData}
              tenantId={tenantId}
              selectedFilter={selectedFilter}
            />
          </div>
        </>
      )}
    </bds-grid>
  );
};
