@import 'blip-ds/dist/collection/styles/_colors.scss';

.helpdesks {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--3, 24px);
  align-self: stretch;
  margin: 24px 0;
  min-height: 100vh;

  &__big-numbers-grid {
    display: grid;
    gap: var(--1, 8px);
    grid-template-columns: repeat(12, 1fr);
    width: 100%;
    > * {
      grid-column: span 3;
    }
  }

  &__performance-graph-container {
    background-color: $color-surface-1;
    border-radius: var(--2, 16px);
    width: 100%;
    gap: var(--3, 24px);
    padding: var(--2, 16px);
    display: grid;
    grid-template-columns: repeat(12, 1fr);

    :nth-child(1) {
      grid-column: span 8;
    }
    :nth-child(2) {
      grid-column: span 4;
    }
  }

  &__table-tickets-panel-container {
    width: 100%;
  }

  &__filter-container {
    width: 100%;
  }

  &__table-tickets-heat-map {
    width: 100%;
  }

  &__loading-overlay {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }

  &__no-data-message {
    > bds-typo {
      margin-top: var(--2, 32px);
      margin-bottom: var(--2, 32px);
      margin-left: var(--2, 32px);
    }

    background: var(--color-surface-1, #F6F6F6);
    border-radius: var(--2, 16px);
    min-height: 40px;
    grid-column-start: 1;
    grid-column-end: 13;
    display: flex;
  }
}

@media screen and (max-width: 1366px) {
  .helpdesks {
    &__performance-graph-container {
      :nth-child(1) {
        grid-column: span 7;
      }
      :nth-child(2) {
        grid-column: span 5;
      }
    }

    &__big-numbers-grid {
      :nth-child(-n + 6) {
        grid-column: span 4;
      }
      :nth-last-child(-n + 2) {
        grid-column: span 6;
      }
    }
  }
}
