import { Chart } from 'chart.js';

interface DatasetConfigProps {
  percentageAnswered: number[];
  chartColor?: string;
}

export const getDatasets = ({ percentageAnswered, chartColor = 'rgba(240, 99, 5, 1)' }: DatasetConfigProps) => [
  {
    type: 'line' as const,
    label: '% de respostas',
    borderColor: chartColor,
    borderWidth: 2,
    backgroundColor: chartColor,
    fill: true,
    data: percentageAnswered,
  },
];

export const options: any = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        generateLabels: (chart: Chart) => {
          return chart.data.datasets.map((dataset) => ({
            text: dataset.label,
            fillStyle: dataset.borderColor,
            lineWidth: 2,
            strokeStyle: dataset.borderColor || 'black',
            pointStyle: 'line',
          }));
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          return `${tooltipItem.raw}% de respondidas`;
        },
      },
    },
  },
  scales: {
    x: {
      offset: true,
      grid: {
        drawOnChartArea: false,
        color: 'transparent',
      },
      ticks: {
        padding: 10,
      },
    },
    y: {
      type: 'linear',
      position: 'left',
      grid: {
        drawOnChartArea: true,
        color: 'rgba(0,0,0,.20)',
      },
      ticks: {
        callback: (value: any) => `${value}%`,
        suggestedMin: 0,
        suggestedMax: 20,
        padding: 5,
      },
    },
  },
};
