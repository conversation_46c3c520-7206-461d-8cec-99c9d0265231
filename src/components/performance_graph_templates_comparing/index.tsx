import React from 'react';
import { translation } from './i18n/translation';
import { useTranslation } from '@hooks/useTranslation';
import LineBarChart from '@components/line_bar_chart';
import { getDatasets, options } from './graph_settings';

interface PerformanceGraphProps {
  labelPeriods: string[];
  percentageAnswered: number[];
  chartColor?: string;
}

const PerformanceGraphTemplatesComparing = ({
  labelPeriods,
  percentageAnswered,
  chartColor,
}: PerformanceGraphProps) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const datasets = getDatasets({
    percentageAnswered,
    chartColor,
  });

  return datasets && datasets.some((dataset) => dataset.data.length > 0) ? (
    <LineBarChart
      title={translations.blip_insights_performance_graph.title}
      description={translations.blip_insights_performance_graph.subtitle}
      labelPeriods={labelPeriods}
      datasets={datasets}
      chartOptions={options}
      hasSelect={false}
      chartType={'line'}
    />
  ) : (
    <div className="proactive-messages__no-data-message">
      <bds-typo variant="fs-16" bold="bold">
        Não há dados para o filtro selecionado - Performance ao longo do tempo
      </bds-typo>
    </div>
  );
};

export default PerformanceGraphTemplatesComparing;
