import React, { useRef, useState, useEffect } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import './insights.scss';

interface InsightsProps {
  textInsight: string;
  loading?: boolean;
}

export const Insights: React.FC<InsightsProps> = ({ textInsight, loading = false }) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  const textRef = useRef<HTMLParagraphElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    const el = textRef.current;
    if (!el) return;

    const computeOverflow = () => {
      const computedStyle = getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 20;
      const maxLines = 8;
      const totalLines = Math.ceil(el.scrollHeight / lineHeight);
      setIsOverflowing(totalLines > maxLines);
    };

    computeOverflow();

    const resizeObserver = new ResizeObserver(() => {
      computeOverflow();
    });

    resizeObserver.observe(el);

    return () => {
      resizeObserver.disconnect();
    };
  }, [textInsight, loading]);

  const toggleExpand = () => setIsExpanded((prev) => !prev);

  return (
    <bds-grid class="insights" gap="1">
      <bds-grid class="insights__header" gap="1">
        <div className="insights__icon">
          <bds-illustration type="spots" name="rocket"></bds-illustration>
        </div>
        <h1 className="insights__title">{translations.blip_insights_insights.title}</h1>
      </bds-grid>
      <bds-grid class="insights__content">
        {loading ? (
          <span>{translations.blip_insights_insights.loading}</span>
        ) : (
          <p ref={textRef} className={`insights__text ${isExpanded ? 'expanded' : ''}`}>
            {textInsight}
          </p>
        )}

        {isOverflowing && (
          <button className="insights__toggle" onClick={toggleExpand}>
            {isExpanded ? translations.blip_insights_insights.seeLess : translations.blip_insights_insights.seeMore}
          </button>
        )}
      </bds-grid>
    </bds-grid>
  );
};
