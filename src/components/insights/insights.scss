@import 'blip-ds/dist/collection/styles/_colors.scss';

.insights {
  border-radius: var(--1, 8px);
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  background-color: $color-surface-1;
  flex-direction: column;
  width: 100%;

  &__header {
    padding: var(--1, 8px) var(--2, 16px) var(--0, 0px) var(--2, 16px);
    align-items: center;
    gap: var(--1, 8px);
  }

  &__icon {
    height: 48px;
  }

  &__title {
    color: var(--content-color-content-default, var(--color-content-default, #454545));
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    margin: 0;
  }

  &__content {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--1, 0px) var(--2, 16px) var(--0, 16px) var(--2, 16px);
    max-height: 300px;
    margin-top: var(--2, 16px);
  }

  &__text {
    color: var(--color-content-default, #454545);
    font-size: 12px;
    line-height: 20px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 8;
    line-clamp: 8;
    -webkit-box-orient: vertical;
    transition: all 0.3s ease;
    margin: 0;
    max-height: 220px;

    &.expanded {
      -webkit-line-clamp: unset;
      line-clamp: unset;
      display: block;
      overflow-y: auto;
    }
  }

  &__toggle {
    background: none;
    color: var(--color-primary, #1e6bf1);
    cursor: pointer;
    font-size: 12px;
    line-height: 20px;
    height: auto;
    min-width: 0;
    padding: 0;
  }
}
