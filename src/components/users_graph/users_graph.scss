@import '~blip-ds/dist/collection/styles/_colors.scss';

.messages-graph {
  display: flex;
  flex-direction: column;
  padding: var(--2, 16px);
  align-items: flex-start;
  gap: var(--3, 24px);
  align-self: stretch;
  border-radius: var(--2, 16px);
  background: var(--color-surface-1, #f6f6f6);
  width: 100%;
  height: 360px;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  &__title {
    color: var(
      --content-color-content-default,
      var(--color-content-default, #454545)
    );
    font-family: 'Nunito Sans';
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
  }

  &__description {
    color: var(--color-content-disable, #636363);
    font-family: 'Nunito Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  &__chart-container {
    width: 100%;
    height: 80%;
  }
}

.messages-chart-graph {
  width: 100%;
  height: 100%;
}
