import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { Chart } from 'chart.js';

interface DatasetConfigProps {
  dailyActiveUsers: number[];
  engagedUsers: number[];
  engagementRate: number[];
  recurrencyRate: number[];
}

export const getDatasets = ({
  dailyActiveUsers,
  engagedUsers,
  engagementRate,
  recurrencyRate,
}: DatasetConfigProps) => [
  {
    label: 'Usuários ativos por dia',
    type: 'bar' as const,
    backgroundColor: 'rgba(178, 223, 253, 1)',
    data: dailyActiveUsers,
    borderColor: 'rgba(178, 223, 253, 1)',
    borderWidth: 2,
    borderRadius: 10,
    minBarLength: 33,
    order: 1,
  },
  {
    label: 'Usuários engajados',
    type: 'bar' as const,
    backgroundColor: 'rgba(128, 227, 235, 1)',
    data: engagedUsers,
    borderColor: 'rgba(128, 227, 235, 1)',
    borderWidth: 2,
    minBarLength: 33,
    borderRadius: 10,
    order: 1,
  },
  {
    label: 'Nível de engajamento (%)',
    type: 'line' as const,
    borderColor: 'rgba(251, 75, 193, 1)',
    borderWidth: 2,
    backgroundColor: 'rgba(251, 75, 193, 1)',
    data: engagementRate,
    yAxisID: 'y1',
  },
  {
    label: 'Nível de recorrência (%)',
    type: 'line' as const,
    borderColor: 'rgba(25, 104, 240, 1)',
    borderWidth: 2,
    backgroundColor: 'rgba(25, 104, 240, 1)',
    data: recurrencyRate,
    yAxisID: 'y1',
  },
];
export const options: any = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        generateLabels: (chart: Chart) => {
          return chart.data.datasets.map(dataset => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            lineWidth: dataset.type === 'line' ? 2 : 0,
            strokeStyle: dataset.borderColor || 'black',
            pointStyle: dataset.type === 'line' ? 'dash' : 'rectRounded',
          }));
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          const labels = ['Nível de engajamento (%)', 'Nível de recorrência (%)'];
          if (labels.includes(tooltipItem.dataset.label)) {
            return `${
              tooltipItem.raw
            }% de ${tooltipItem.dataset.label.replace(' (%)', '').toLowerCase()}`;
          }
          return `${formatNumberForChart(tooltipItem.raw)} ${tooltipItem.dataset.label.toLowerCase()}`;
        },
      },
    },
  },
  scales: {
    x: {
      type: 'category',
      grid: {
        drawOnChartArea: false,
      },
      barPercentage: 0.8, // Adjust bar width within a category
      categoryPercentage: 0.9, // Adjust spacing between categories
    },
    y: {
      type: 'linear',
      position: 'left',
      grid: {
        drawOnChartArea: true,
        color: 'rgba(0,0,0,.20)',
      },
      ticks: {
        stepSize: 20,
        padding: 5,
      },
    },
    y1: {
      type: 'linear',
      position: 'right',
      ticks: {
        callback: (value: any) => `${value}%`,
        min: 0,
        max: 100,
        stepSize: 25,
        padding: 10,
      },
      grid: {
        drawOnChartArea: false,
        color: 'rgba(0,0,0,.16)',
      },
    },
  },
};
