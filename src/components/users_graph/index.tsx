import React from 'react';
import { translation } from './i18n/translation';
import { useTranslation } from '@hooks/useTranslation';
import { getDatasets, options } from './graph_settings';
import './users_graph.scss';
import LineBarChart from '@components/line_bar_chart';

export interface UsersGraphData {
  labelPeriods: string[];
  engagedUsers: number[];
  engagementRate: number[];
  dailyActiveUsers: number[];
  recurrencyRate: number[];
}

const UsersGraph = (data: UsersGraphData) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const datasets = getDatasets({
    dailyActiveUsers: data.dailyActiveUsers,
    engagedUsers: data.engagedUsers,
    engagementRate: data.engagementRate,
    recurrencyRate: data.recurrencyRate,
  });

  return (
    <LineBarChart
      title={translations.blip_insights_users_graph.title}
      description={translations.blip_insights_users_graph.subtitle}
      labelPeriods={data.labelPeriods}
      datasets={datasets}
      chartOptions={options}
    />
  );
};

export default UsersGraph;
