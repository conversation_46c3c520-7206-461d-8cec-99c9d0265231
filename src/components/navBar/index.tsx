import React from 'react';
import './navBar.scss';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';

export const NavBar = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  return (
    <nav className="navbar">
      <div className="navbar__logo">
        <a href="/" className="animated bounceIn">
          <bds-illustration
            type="brand"
            name="blip-ballon-blue-white-horizontal"
            alt={translations.blip_insights_navbar.brandLogo.alt}
            title={translations.blip_insights_navbar.brandLogo.title}
          />
        </a>
      </div>
    </nav>
  );
};
