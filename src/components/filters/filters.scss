@import '~blip-ds/dist/collection/styles/_colors.scss';

@mixin grid-template-columns-filter($columns: 4) {
  grid-template-columns: repeat($columns, 1fr);
}

@mixin grid-column-span-filter($columns: 2) {
  grid-column: span $columns;
}

.filters {
  display: grid;
  gap: var(--2, 16px);

  &__title {
    display: flex;
    gap: var(--1, 8px);
  }

  &__description {
    color: var(--content-color-content-default, #292929);
    font-family: 'Nunito Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px; /* 157.143% */
  }

  &__select-container {
    display: grid;
    gap: var(--1, 8px);
  }

  &__select {
    display: flex;
    align-items: flex-start;
    gap: var(--1, 8px);
    align-self: stretch;
  }

  &__container-date {
    display: grid;
    gap: var(--2, 16px);
    @include grid-template-columns-filter();
  }

  &__select-date {
    @include grid-column-span-filter();
  }

  &__button-container {
    display: flex;
    align-items: center;
    gap: var(--1, 8px);
    align-self: stretch;
    @include grid-column-span-filter();
  }
}
