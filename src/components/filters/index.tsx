import React from 'react';
import './filters.scss';
import { BdsButton, BdsTooltip, BdsTypo } from 'blip-ds/dist/blip-ds-react/components';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import DateSelect, { DateSelectProps } from '@components/date_select';
import { TooltipPostionType } from 'blip-ds/dist/types/components/tooltip/tooltip';

type FiltersProps = DateSelectProps & {
  children: JSX.Element[] | JSX.Element;
  onApplyFilter: () => void;
  clearFilter: () => void;
  applyButtonDisabled?: boolean;
  clearButtonDisabled?: boolean;
  hasTooltip?: boolean;
  tooltipText?: string;
  tooltipPosition?:
    | 'top-center'
    | 'top-left'
    | 'top-right'
    | 'left-center'
    | 'left-top'
    | 'left-bottom'
    | 'bottom-center'
    | 'bottom-right'
    | 'bottom-left'
    | 'right-center'
    | 'right-top'
    | 'right-bottom';
};

const Filters = ({
  children,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onConcludeDateClick,
  onApplyFilter,
  clearFilter,
  applyButtonDisabled = false,
  clearButtonDisabled = false,
  hasTooltip = false,
  tooltipText = '',
  tooltipPosition = 'bottom-center',
}: FiltersProps) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  return (
    <div className="filters">
      <div className="filters__select-container">
        <div className="filters__title">
          <BdsTypo variant="fs-14" bold="bold" className="filters__description">
            {translations.blip_insights_filters.description.main_filter}
          </BdsTypo>
          {hasTooltip && (
            <BdsTooltip
              id="variation-tooltip"
              tooltip-text={tooltipText}
              position={tooltipPosition as TooltipPostionType}
              class="filters__tooltip"
            >
              <bds-icon name="info" size="small"></bds-icon>
            </BdsTooltip>
          )}
        </div>
        <div className="filters__select">{children}</div>
      </div>
      <div className="filters__container-date">
        <div className="filters__select-date">
          <DateSelect
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={onStartDateChange}
            onEndDateChange={onEndDateChange}
            onConcludeDateClick={onConcludeDateClick}
          />
        </div>
        <div className="filters__button-container">
          <BdsButton onClick={() => !applyButtonDisabled && onApplyFilter()} disabled={applyButtonDisabled}>
            {translations.blip_insights_filters.button.apply_filter}
          </BdsButton>
          <BdsButton
            variant="outline"
            onClick={() => !clearButtonDisabled && clearFilter()}
            disabled={clearButtonDisabled}
          >
            {translations.blip_insights_filters.button.clear_filter}
          </BdsButton>
        </div>
      </div>
    </div>
  );
};

export default Filters;
