import React, { useEffect } from 'react';

interface BlipChatWidgetProps {
  tenantId: string;
}

const BlipChatWidget: React.FC<BlipChatWidgetProps> = ({ tenantId }) => {
  useEffect(() => {
    const scriptId = 'blip-chat-widget-script';

    if (!document.getElementById(scriptId)) {
      const script = document.createElement('script');
      script.id = scriptId;
      script.src = 'https://unpkg.com/blip-chat-widget@1.11.*';
      script.type = 'text/javascript';
      script.async = true;

      script.onload = () => {
        initializeBlipChat(tenantId);
      };

      document.body.appendChild(script);
    } else {
      initializeBlipChat(tenantId);
    }

    return () => {
      const script = document.getElementById('blip-chat-widget-script');
      if (script) document.body.removeChild(script);
      if ((window as any).BlipChat) delete (window as any).BlipChat;
    };
    
  }, [tenantId]);

  const initializeBlipChat = (tenantId: string) => {
    if ((window as any).BlipChat) {
      const blipClient = new (window as any).BlipChat();
      blipClient.withAppKey('YmxpcGluc2lnaHRzMTplMWViYWQ2Yy05NDEzLTQ4MjYtYTU0Zi1lODUwZDU4OTgxNDU=')
        .withButton({ color: '#0096fa', icon: '' })
        .withCustomCommonUrl('https://contatointeligentetake.chat.blip.ai/')
        .withEventHandler((window as any).BlipChat.LOAD_EVENT, () => {
          blipClient.sendMessage({
            type: 'text/plain',
            content: `TenantId:${tenantId}`,
            metadata: {
              '#blip.hiddenMessage': true,
            },
          });
        })
        .build();
    } else {
      console.error('BlipChat is not available on window');
    }
  };

  return null;
};

export default BlipChatWidget;