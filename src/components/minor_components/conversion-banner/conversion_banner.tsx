import React from 'react';
import { translation } from './i18n/translations';
import { useTranslation } from '@hooks/useTranslation';

const ConversionBanner = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  return (
    <div className="conversion-banner">
      <bds-banner variant="system" button-close={true} context="inside">
        {translations.conversion_banner.content}
      </bds-banner>
    </div>
  );
};

export default ConversionBanner;