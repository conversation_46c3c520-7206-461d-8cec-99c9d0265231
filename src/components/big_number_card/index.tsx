import React from 'react';
import './big_number_card.scss';
import { translation } from './i18n/translation';
import { TooltipPostionType } from 'blip-ds/dist/types/components/tooltip/tooltip';

export interface BigNumberCardProps {
  type: 'integer' | 'period' | 'percentage';
  variation: any;
  value: string;
  title: string;
  tooltip: {
    text: string;
    position: TooltipPostionType;
  };
  icon: string;
  chipColor: 'outline' | 'default' | 'info' | 'success' | 'warning' | 'danger' | 'disabled' | undefined;
}

const BigNumberCard = ({ type, value, title, variation, tooltip, icon, chipColor }: BigNumberCardProps) => {
  return (
    <div className="big-number-card__body">
      <div className="big-number-card__icon-container">
        <bds-icon name={icon} theme="outline" size="medium" class="big-number-card__icon"></bds-icon>
      </div>
      <div className="big-number-card__content">
        <div className="big-number-card__variation">
          <bds-chip-tag class="big-number-card__variation-chip" color={chipColor}>
            {variation}
          </bds-chip-tag>
          {translation.pt.blip_insights_bignumbers.card.body.variations.period}
        </div>
        <div className="big-number-card__value">{value}</div>
        <div className="big-number-card__label">
          {title}
          <bds-tooltip
            id="variation-tooltip"
            tooltip-text={tooltip.text}
            position={tooltip.position}
            class="big-number-card__tooltip"
          >
            <bds-icon name="info" size="x-small"></bds-icon>
          </bds-tooltip>
        </div>
      </div>
    </div>
  );
};

export default BigNumberCard;
