@import '~blip-ds/dist/collection/styles/_colors.scss';

.big-number-card {
  &__body {
    display: flex;
    padding: var(--2, 16px);
    align-items: flex-start;
    gap: var(--1, 8px);
    border-radius: var(--2, 16px);
    background: var(--color-surface-1, #f6f6f6);
  }

  &__value {
    color: var(--content-color-content-default, #454545);

    font-family: 'Nunito Sans';
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: 48px;
  }

  &__icon-container {
    display: flex;
    padding: var(--1, 8px);
    justify-content: center;
    align-items: center;
    gap: var(--0, 0px);

    border-radius: var(--1, 8px);
    background: var(--color-surface-2, #e0e0e0);
  }

  &__icon {
    display: flex;
    align-items: center;
    font-size: var(--2, 24px);
    background: var(--color-surface-2, #e0e0e0);
  }

  &__variation {
    display: flex;
    padding: var(--0, 0px);
    align-items: center;
    gap: var(--05, 4px);
    margin-bottom: 8px;

    color: var(--content-color-content-default, #454545);
    font-family: 'Nunito Sans';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  &__label {
    display: flex;
    padding: var(--0, 0px);
    align-items: center;
    gap: var(--05, 4px);
    color: var(--color-content-default, #454545);

    font-family: 'Nunito Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  &__tooltip{
    max-height: 16px;
  }
}
