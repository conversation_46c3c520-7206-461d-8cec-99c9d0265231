import {
  BdsTable,
  BdsTableBody,
  BdsTableCell,
  BdsTableHeader,
  BdsTableRow,
  BdsTableTh,
  BdsTypo,
} from 'blip-ds/dist/blip-ds-react/components';
import React, { useEffect, useState } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import './heat_map.scss';

interface HeatMapProps {
  title: string;
  subtitle: string;
  data: number[][];
  interval: number;
  lineLabels: string[];
  onChangeSelect?: (v: string) => void;
  children?: React.ReactNode;
  isLoading?: boolean;
  view?: string;
}

const HeatMap = ({ title, subtitle, data, interval, isLoading = false, lineLabels, children, view }: HeatMapProps) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [headingHours, setHeadingHours] = useState<string[]>([]);

  useEffect(() => {
    const result = [];
    for (let i = 0; i <= 23; i++) {
      const resultHour = `${i.toString().padStart(2, '0')}h`;
      result.push(resultHour);
    }
    setHeadingHours(result);
  }, []);

  const hasData = data && data.length > 0;

  return (
    <div className="tickets-heat-map">
      <div className="tickets-heat-map__header">
        <span>
          <h4 className="tickets-heat-map__title">{title}</h4>
          <p className="tickets-heat-map__description">{subtitle}</p>
        </span>
        {children && <div className="tickets-heat-map__extra">{children}</div>}
      </div>
      {isLoading ? (
        <p>Carregando...</p>
      ) : hasData ? (
        <BdsTable className="tickets-heat-map__table" key={view || undefined}>
          <BdsTableHeader>
            <BdsTableRow>
              <BdsTableTh className="tickets-heat-map__weekday-col">
                <BdsTypo variant="fs-14" bold="bold">
                  {translations.blip_insights_heatmap.table.dayOfTheWeek}
                </BdsTypo>
              </BdsTableTh>
              {headingHours.map((item) => (
                <BdsTableTh key={item}>
                  <BdsTypo variant="fs-14" bold="bold">
                    {item}
                  </BdsTypo>
                </BdsTableTh>
              ))}
            </BdsTableRow>
          </BdsTableHeader>
          <BdsTableBody>
            {data.map((row, index) => (
              <BdsTableRow key={index}>
                <BdsTableCell>
                  <BdsTypo variant="fs-14" bold="bold">
                    {lineLabels[index]}
                  </BdsTypo>
                </BdsTableCell>
                {row.map((value, index) => {
                  const cellClass =
                    value === 0
                      ? '--empty'
                      : value <= interval * 0.33
                        ? '--min'
                        : value <= interval * 0.66
                          ? '--mid'
                          : '--max';
                  return (
                    <BdsTableCell key={index} className={`tickets-heat-map__table__table-cell${cellClass}`}>
                      <BdsTypo variant="fs-14">{value.toLocaleString()}</BdsTypo>
                    </BdsTableCell>
                  );
                })}
              </BdsTableRow>
            ))}
          </BdsTableBody>
        </BdsTable>
      ) : (
        <div className="tickets-heat-map__no-data">
          <BdsTypo variant="fs-16" bold="bold">
            Não há dados para o filtro selecionado
          </BdsTypo>
        </div>
      )}
    </div>
  );
};

export default HeatMap;
