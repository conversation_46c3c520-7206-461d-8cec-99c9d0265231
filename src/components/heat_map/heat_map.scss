@import '~blip-ds/dist/collection/styles/_colors.scss';

.tickets-heat-map {
  display: flex;
  flex-direction: column;
  padding: var(--2, 16px);
  align-items: flex-start;
  gap: var(--3, 24px);
  align-self: stretch;
  border-radius: var(--2, 16px);
  background: var(--color-surface-1, #f6f6f6);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  &__title {
    color: var(--content-color-content-default, var(--color-content-default, #454545));
    /* $fs-20-h4/Bold */
    font-family: 'Nunito Sans';
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px; /* 140% */
    margin: 0;
  }

  &__description {
    color: var(--color-content-disable, #636363);
    /* $fs-14-p2/Regular */
    font-family: 'Nunito Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin: 0;
  }

  &__table {
    display: flex;
    overflow-x: auto;

    bds-table-row,
    bds-table-header {
      border-bottom: none;
    }

    bds-table-cell:first-child {
      & > div {
        justify-content: start !important;
      }
    }

    bds-table-cell {
      padding: 0 8px !important;
      & > div {
        justify-content: center !important;
      }
    }

    &__table-cell {
      &--empty {
        background-color: $color-surface-2;
      }
      &--min {
        background-color: $color-extended-blue-bright;
      }
      &--mid {
        background-color: $color-extended-blue;
        opacity: 0.75;
        bds-typo {
          color: white;
        }
      }
      &--max {
        background-color: $color-extended-blue;
        bds-typo {
          color: white;
        }
      }
    }
  }

  &__weekday-col {
    white-space: nowrap;
  }
}
