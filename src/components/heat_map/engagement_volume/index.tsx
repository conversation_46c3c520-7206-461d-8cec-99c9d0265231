import React, { useState, useEffect, useMemo, useRef } from 'react';
import HeatMap from '@components/heat_map';
import {
  getEngagementHeatmapReadQuery,
  getEngagementHeatmapAnsweredQuery,
} from '@services/query/ProactiveMessagesQueryBuilder';
import { useBlipInsightsServices } from '@hooks/useServices';
import { getQueryData } from '@services/QueryFetchers';
import { BdsButtonGroup, BdsButton } from 'blip-ds/dist/blip-ds-react/components';
import { EngagementHeatMapData, formatEngagementHeatMapData } from '@utils/proactiveMessages/EngagementHeatMapHelper';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { toBaseFilterWithDateProp } from '@utils/filters';

interface EngagementVolumeHeatMapProps {
  receiptData: {
    data: number[][];
    interval: number;
    lineLabels: string[];
  }[];
  tenantId: string;
  selectedFilter: any;
}

export const EngagementVolumeHeatMap: React.FC<EngagementVolumeHeatMapProps> = ({
  receiptData,
  tenantId,
  selectedFilter,
}) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [view, setView] = useState<'receipt' | 'read' | 'answered'>('receipt');
  const [receiptDataState, setReceiptDataState] = useState<EngagementHeatMapData>({
    data: [],
    interval: 0,
    lineLabels: [],
  });
  const [readData, setReadData] = useState<EngagementHeatMapData | null>(null);
  const [answeredData, setAnsweredData] = useState<EngagementHeatMapData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { apiBlipInsightsService } = useBlipInsightsServices();
  const buttonGroupRef = useRef<any>(null);

  const currentData = useMemo(() => {
    if (view === 'receipt') return receiptDataState;
    if (view === 'read') return readData;
    if (view === 'answered') return answeredData;
    return null;
  }, [view, receiptDataState, readData, answeredData]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);

      try {
        if (view === 'read' && readData === null) {
          await fetchReadData();
        } else if (view === 'answered' && answeredData === null) {
          await fetchAnsweredData();
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchReadData = async () => {
      const response = await apiBlipInsightsService.getData(toBaseFilterWithDateProp('engagement_heatmap_read', selectedFilter, tenantId));
      const formattedData = formatEngagementHeatMapData(response?.results || []);

      setReadData(
        formattedData?.[0] || {
          data: [],
          interval: 0,
          lineLabels: [],
        }
      );
    };

    const fetchAnsweredData = async () => {
      const response = await apiBlipInsightsService.getData(toBaseFilterWithDateProp('engagement_heatmap_answered', selectedFilter, tenantId));
      const formattedData = formatEngagementHeatMapData(response?.results || []);

      setAnsweredData(
        formattedData?.[0] || {
          data: [],
          interval: 0,
          lineLabels: [],
        }
      );
    };

    fetchData();
  }, [view, tenantId, selectedFilter, apiBlipInsightsService, readData, answeredData]);

  useEffect(() => {
    if (buttonGroupRef.current) buttonGroupRef.current.activateButton(0);
    const initialReceiptData = receiptData?.[0] || {
      data: [],
      interval: 0,
      lineLabels: [],
    };

    setReceiptDataState(initialReceiptData);
    setReadData(null);
    setAnsweredData(null);
    setView('receipt');
  }, [selectedFilter, receiptData]);

  return (
    <div className="engagement-volume-heatmap">
      <HeatMap
        title={translations.engagement_heat_map.title}
        subtitle={translations.engagement_heat_map.subtitle}
        data={currentData?.data || []}
        interval={currentData?.interval || 0}
        lineLabels={currentData?.lineLabels || []}
        isLoading={isLoading}
        view={view}
      >
        <BdsButtonGroup ref={buttonGroupRef} color="content" direction="row" size="medium">
          <BdsButton onClick={() => setView('receipt')}>{translations.engagement_heat_map.views.receipt}</BdsButton>
          <BdsButton onClick={() => setView('read')}>{translations.engagement_heat_map.views.read}</BdsButton>
          <BdsButton onClick={() => setView('answered')}>{translations.engagement_heat_map.views.answered}</BdsButton>
        </BdsButtonGroup>
      </HeatMap>
    </div>
  );
};
