import React, { useState, useEffect, useMemo, useRef } from 'react';
import HeatMap from '@components/heat_map';
import { useBlipInsightsServices } from '@hooks/useServices';
import { formatTicketsHeatMapData, TicketsHeatMapData } from '@utils/helpdesk/TicketsHeatMapHelper';
import { BdsButtonGroup, BdsButton } from 'blip-ds/dist/blip-ds-react/components';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { toHelpdeskBaseFilterWithDataFechamentoProp } from '@utils/filters';

interface TicketsVolumeHeatMapProps {
  openingData: TicketsHeatMapData;
  tenantId: string;
  selectedFilter: any;
}

export const TicketsVolumeHeatMap: React.FC<TicketsVolumeHeatMapProps> = ({
  openingData,
  tenantId,
  selectedFilter,
}) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const [view, setView] = useState<'opening' | 'closing'>('opening');
  const [openingDataState, setOpeningDataState] = useState(openingData);
  const [closingData, setClosingData] = useState<TicketsHeatMapData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { apiBlipInsightsService } = useBlipInsightsServices();
  const buttonGroupRef = useRef<HTMLBdsButtonGroupElement>(null);

  const currentData = useMemo(() => {
    if (view === 'opening') return openingDataState;
    if (view === 'closing') return closingData;
    return null;
  }, [view, openingDataState, closingData]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);

      try {
        if (view === 'closing' && closingData === null) {
          await fetchClosingData();
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchClosingData = async () => {
      const baseFilter = toHelpdeskBaseFilterWithDataFechamentoProp('closing_tickets_heatmap', selectedFilter, tenantId);
      const response = await apiBlipInsightsService.getData(baseFilter);
      const formattedData = formatTicketsHeatMapData(response?.results || []);

      setClosingData(formattedData || null);
    };

    fetchData();
  }, [view, tenantId, selectedFilter, apiBlipInsightsService, closingData]);

  useEffect(() => {
    if (buttonGroupRef.current) {
      buttonGroupRef.current.activateButton(0);
    }
    setOpeningDataState(openingData);
    setClosingData(null);
    setView('opening');
  }, [selectedFilter, openingData]);

  return (
    <div className="tickets-volume-heatmap">
      <HeatMap
        title={translations.tickets_heatmap.title}
        subtitle={translations.tickets_heatmap.subtitle}
        data={currentData?.ticketsData || []}
        interval={currentData?.interval || 0}
        lineLabels={currentData?.lineLabels || []}
        isLoading={isLoading}
      >
        <BdsButtonGroup ref={buttonGroupRef} color="content" direction="row" size="medium">
          <BdsButton onClick={() => setView('opening')}>{translations.tickets_heatmap.views.opening}</BdsButton>
          <BdsButton onClick={() => setView('closing')}>{translations.tickets_heatmap.views.closing}</BdsButton>
        </BdsButtonGroup>
      </HeatMap>
    </div>
  );
};
