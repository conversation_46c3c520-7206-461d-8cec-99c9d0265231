import React, { useEffect, useRef, useCallback } from 'react';
import './header.scss';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { useBlipInsightsServices } from '@hooks/useServices';
import { fetchLastUpdate } from '@services/QueryFetchers';
import { finishedFetchLastDataUpdateEventName, shallRefreshChartsEventName, startedFetchLastDataUpdateEventName, updateClickedEventName } from '@utils/constants';
import { useAppContext } from '@contexts/AppContext';

export const Header = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const { apiBlipInsightsService } = useBlipInsightsServices();
  const [lastUpdateDate, setLastUpdateDate] = React.useState<string | null>(null);
  const [lastUpdateTime, setLastUpdateTime] = React.useState<string | null>(null);
  const [loadedInitialDate, setLoadedInitialDate] = React.useState(false);
  const lastDateRef = useRef<string>('');
  const { setShowToast } = useAppContext();

  const handleToast = useCallback(() => {
    if (setShowToast) {
      setShowToast(true);
      setTimeout(() => setShowToast(false), 8000);
    }
  }, [setShowToast]);

  const fetchLastUpdateData = useCallback(
    async (shallDispatchEvent = false) => {
      document.dispatchEvent(new CustomEvent(startedFetchLastDataUpdateEventName));
      try {
        const response = await fetchLastUpdate(apiBlipInsightsService);
        const resultString = response?.results[0][0] || '';
        if (resultString.split(' ').length === 3) {
          const split = resultString.split(' ');
          setLastUpdateTime(split[2] || null);
          setLastUpdateDate(split[0] || null);
        }

        if (shallDispatchEvent) {
          if (lastDateRef.current.length && lastDateRef.current === resultString) {
            handleToast();
          } else {
            document.dispatchEvent(new CustomEvent(shallRefreshChartsEventName));
          }
        }
        lastDateRef.current = resultString;
      } catch (error) {
        console.error('Error fetching last update:', error);
      } finally {
        document.dispatchEvent(new CustomEvent(finishedFetchLastDataUpdateEventName));
        setLoadedInitialDate(true);
      }
    },
    [apiBlipInsightsService, handleToast]
  );

  const refreshButtonClicked = useCallback(async () => {
    await fetchLastUpdateData(true);
  }, [fetchLastUpdateData]);

  useEffect(() => {
    document.removeEventListener(updateClickedEventName, refreshButtonClicked);
    document.addEventListener(updateClickedEventName, refreshButtonClicked);
    return () => {
      // Code to execute on component destroy
      document.removeEventListener(updateClickedEventName, refreshButtonClicked);
    };
  }, [refreshButtonClicked]);

  useEffect(() => {
    if (!loadedInitialDate) {
      fetchLastUpdateData();
    }
  }, [apiBlipInsightsService, fetchLastUpdateData, loadedInitialDate]);

  return (
    <header className="header">
      <bds-typo variant="fs-24" bold="bold" class="header__text">
        {translations.blip_insights_header.title}
      </bds-typo>
      <bds-typo variant="fs-14" class="header__text">
        {translations.blip_insights_header.subTitle.lastUpdate} {lastUpdateDate}{' '}
        {translations.blip_insights_header.subTitle.at} <b>{lastUpdateTime}</b>
      </bds-typo>
    </header>
  );
};
