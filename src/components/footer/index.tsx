import React, { useRef } from 'react';
import './footer.scss';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';

export const Footer = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  return (
    <bds-grid class="footer-container" direction="column">
      <footer className="footer">
        <bds-typo variant="fs-12" class="footer__text">
          © {new Date().getFullYear()} {translations.blip_insights_footer.poweredByBlip} |{' '}
          {translations.blip_insights_footer.allRightsReserved} |
          <a
            href="https://help.blip.ai/hc/pt-br/articles/360054956974"
            target="_blank"
            rel="noopener noreferrer"
            className="footer__link"
          >
            &nbsp;{translations.blip_insights_footer.termsOfUse}
          </a>
        </bds-typo>
      </footer>
    </bds-grid>
  );
};
