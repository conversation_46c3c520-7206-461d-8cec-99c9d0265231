import React from 'react';

import { translation } from './i18n/translation';
import { useTranslation } from '@hooks/useTranslation';
import LineBarChart from '@components/line_bar_chart';
import { getDatasets, options } from './graph_settings';

interface PerformanceGraphProps {
  labelPeriods: string[];
  messagesSent: number[];
  messagesReceived: number[];
  messagesResponded: number[];
  responsesCount: number[];
}

const PerformanceGraphProactiveMessages = ({
  labelPeriods,
  messagesSent,
  messagesReceived,
  messagesResponded,
  responsesCount,
}: PerformanceGraphProps) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const datasets = getDatasets({
    messagesSent,
    messagesReceived,
    messagesResponded,
    responsesCount,
  });

  return (
    <LineBarChart
      title={translations.blip_insights_performance_graph.title}
      description={translations.blip_insights_performance_graph.subtitle}
      labelPeriods={labelPeriods}
      datasets={datasets}
      defaultSelectedOption="Engajamento"
      chartOptions={options}
      hasSelect={false}
    />
  );
};

export default PerformanceGraphProactiveMessages;
