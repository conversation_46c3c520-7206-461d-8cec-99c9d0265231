import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { Chart } from 'chart.js';

type ResponsesCount = {
  [key: string]: number[];
};

type SelectOption = {
  [key: string]: string;
};

interface DatasetConfigProps {
  messagesSent: number[];
  messagesReceived: number[];
  messagesResponded: number[];
  responsesCount: number[];
}

export const getDatasets = ({
  messagesSent,
  messagesReceived,
  messagesResponded,
  responsesCount,
}: DatasetConfigProps) => [
  {
    type: 'line' as const,
    label: 'Nível de engajamento (%)',
    borderColor: 'rgba(240, 99, 5, 1)',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 99, 5, 1)',
    fill: true,
    data: responsesCount,
    yAxisID: 'y1',
  },
  {
    type: 'bar' as const,
    label: 'Mensagens enviadas',
    backgroundColor: 'rgba(25, 104, 240, 0.65)',
    data: messagesSent,
    borderColor: 'rgba(25, 104, 240, 1)',
    borderWidth: 2,
    borderRadius: 10,
  },
  {
    type: 'bar' as const,
    label: 'Mensagens recebidas',
    backgroundColor: 'rgba(253, 155, 220, 0.65)',
    data: messagesReceived,
    borderColor: 'rgba(253, 155, 220, 1)',
    borderWidth: 2,
    borderRadius: 10,
  },
  {
    type: 'bar' as const,
    label: 'Mensagens respondidas',
    backgroundColor: 'rgba(132, 235, 188, 0.65)',
    data: messagesResponded,
    borderColor: 'rgba(132, 235, 188, 1)',
    borderWidth: 2,
    borderRadius: 10,
  },
];

export const options: any = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        generateLabels: (chart: Chart) => {
          return chart.data.datasets.map((dataset) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            lineWidth: dataset.type === 'line' ? 2 : 0,
            strokeStyle: dataset.borderColor || 'black',
            pointStyle: dataset.type === 'line' ? 'dash' : 'rectRounded',
          }));
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          const labels = [
            'Nível de engajamento (%)',
          ];
          if (labels.includes(tooltipItem.dataset.label)) {
            return `${tooltipItem.raw}% de ${tooltipItem.dataset.label.replace(' (%)', '').toLowerCase()}`;
          }
          return `${formatNumberForChart(tooltipItem.raw)} ${tooltipItem.dataset.label.toLowerCase()}`;
        },
      },
    },
  },
  scales: {
    y: {
      type: 'linear',
      position: 'left',
      grid: {
        drawOnChartArea: true,
        color: 'rgba(0,0,0,.20)',
      },
      ticks: {
        stepSize: 20,
        padding: 5,
      },
    },
    y1: {
      type: 'linear',
      position: 'right',
      ticks: {
        callback: (value: any) => `${value}%`,
        min: 0,
        max: 100,
        stepSize: 25,
        padding: 10,
      },
      grid: {
        drawOnChartArea: false,
        color: 'rgba(0,0,0,.16)',
      },
    },
  },
};
