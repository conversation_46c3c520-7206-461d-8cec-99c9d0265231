import React, { useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Toolt<PERSON>,
  Legend,
  LineController,
  BarElement,
  BarController,
} from 'chart.js';
import { Chart as ReactChart } from 'react-chartjs-2';
import { BdsSelect } from 'blip-ds/dist/blip-ds-react/components';
import './line_bar_chart.scss';

ChartJS.register(
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  Tooltip,
  LineController,
  BarController
);

ChartJS.defaults.font = {
  family: "'Nunito Sans', sans-serif",
  size: 14,
  weight: 'normal',
  style: 'normal',
};

interface LineBarChartBaseProps {
  title: string;
  description: string;
  labelPeriods: string[];
  datasets: any[];
  chartOptions: any;
  hasSelect?: boolean;
  onSelectChange?: (selectedOption: string) => void;
  chartType?: 'line' | 'bar';
}

interface LineBarChartWithSelectProps extends LineBarChartBaseProps {
  hasSelect: true;
  selectOptions: { [key: string]: string };
  defaultSelectedOption: string;
}

interface LineBarChartWithoutSelectProps extends LineBarChartBaseProps {
  hasSelect?: false;
  selectOptions?: { [key: string]: string };
  defaultSelectedOption?: string;
}

type LineBarChartProps = LineBarChartWithSelectProps | LineBarChartWithoutSelectProps;

const LineBarChart: React.FC<LineBarChartProps> = ({
  title,
  description,
  labelPeriods,
  selectOptions,
  datasets,
  defaultSelectedOption,
  onSelectChange,
  chartOptions,
  hasSelect = false,
  chartType = 'bar',
}) => {
  const [selectedOption, setSelectedOption] = useState<string>(defaultSelectedOption || '');

  const hasData =
    datasets && datasets.length > 0 && datasets.some((dataset) => dataset.data && dataset.data.length > 0);

  const data = hasData ? {
    labels: labelPeriods,
    datasets: datasets.map((dataset) => ({
      ...dataset,
      data: dataset.data[selectedOption] || dataset.data,
      label: dataset.label[selectedOption] || dataset.label,
    })),
  } : {labels: [], datasets: []};

  return (
    <div className={`line-bar-chart ${!hasData ? 'line-bar-chart__no-data' : ''}`}>
      <div className="line-bar-chart__header">
        <span>
          <h4 className="line-bar-chart__title">{title}</h4>
          <p className="line-bar-chart__description">{description}</p>
        </span>
        {hasSelect && selectOptions && (
          <BdsSelect
            onBdsChange={(e) => setSelectedOption(e.detail.value)}
            placeholder="Selecione a métrica"
            options={Object.keys(selectOptions).map((key) => ({
              label: key,
              value: key,
            }))}
            value={selectedOption}
          />
        )}
      </div>
      <div className="line-bar-chart__container">
        {hasData ? (
          <ReactChart type={chartType} data={data} options={chartOptions} />
        ) : (
          <div className="line-bar-chart__no-data">
            <bds-typo variant="fs-16" bold="bold">
              Não há dados para o filtro selecionado
            </bds-typo>
          </div>
        )}
      </div>
    </div>
  );
};

export default LineBarChart;
