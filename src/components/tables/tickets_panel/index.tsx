import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useBlipInsightsServices } from '@hooks/useServices';
import { HelpdeskFilter } from '@typings/HelpdeskFilter';
import {
  BdsButtonGroup,
  BdsButton,
} from 'blip-ds/dist/blip-ds-react/components';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { processTableData, sortTableData } from '@utils/tableUtils';
import TemplateTable from '../template_table';
import './tickets_panel.scss';
import { toHelpdeskBaseFilter } from '@utils/filters';

interface TableTicketsPanelProps {
  data: Array<Array<string | number | null>>;
  tenantId: string;
  selectedFilter: HelpdeskFilter;
}

export const TableTicketsPanel: React.FC<TableTicketsPanelProps> = ({ data, tenantId, selectedFilter }) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  const [view, setView] = useState<'queues' | 'attendants' | 'tags'>('queues');
  const [queueData, setQueueData] = useState(data);
  const [attendantsData, setAttendantsData] = useState<Array<Array<string | number>> | null>(null);
  const [tagsData, setTagsData] = useState<Array<Array<string | number>> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sortedColumn, setSortedColumn] = useState<number | null>(null);
  const [sortDirection, setSortDirection] = useState(1);
  const [arrow, setArrow] = useState('');

  const { apiBlipInsightsService } = useBlipInsightsServices();
  const buttonGroupRef = useRef<any>(null);

  const heading = [
    view === 'queues'
      ? translations.blip_insights_tickets_panel.headings.queues
      : view === 'attendants'
        ? translations.blip_insights_tickets_panel.headings.attendants
        : translations.blip_insights_tickets_panel.headings.tags,
    translations.blip_insights_tickets_panel.headings.closedTickets,
    translations.blip_insights_tickets_panel.headings.lostTickets,
    translations.blip_insights_tickets_panel.headings.abandonedTickets,
    translations.blip_insights_tickets_panel.headings.avgServiceTime,
    translations.blip_insights_tickets_panel.headings.avgWaitTime,
    translations.blip_insights_tickets_panel.headings.avgResponseTime,
    translations.blip_insights_tickets_panel.headings.avgFirstResponseTime,
  ];

  const columnTypes = useMemo(() => ['string', 'number', 'number', 'number', 'time', 'time', 'time', 'time'], []);

  const currentData = useMemo(() => {
    let rawData: Array<Array<string | number | null>> | null = null;

    if (view === 'queues') rawData = queueData;
    if (view === 'attendants') rawData = attendantsData;
    if (view === 'tags') rawData = tagsData;

    return processTableData(rawData || [], columnTypes);
  }, [view, queueData, attendantsData, tagsData, columnTypes]);


  const isScrollable = useMemo(() => {
    return Array.isArray(currentData) && currentData.length > 6;
  }, [currentData]);

  const tableData = useMemo(() => {
    if (sortedColumn === null) return currentData;
    return sortTableData(currentData, columnTypes, sortedColumn, sortDirection);
  }, [currentData, sortedColumn, sortDirection, columnTypes]);

  const toggleSort = (columnIndex: number) => {
    setSortedColumn((prevColumn) => {
      const isSameColumn = prevColumn === columnIndex;
      const newDirection = isSameColumn ? -sortDirection : 1;
      setSortDirection(newDirection);
      setArrow(newDirection === 1 ? 'asc' : 'dsc');
      return columnIndex;
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);

      try {
        if (view === 'attendants' && attendantsData === null) {
          await fetchAttendants();
        } else if (view === 'tags' && tagsData === null) {
          await fetchTags();
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchAttendants = async () => {
      const baseFilter = toHelpdeskBaseFilter('tickets_panel_attendants', selectedFilter, tenantId);
      const response = await apiBlipInsightsService.getData(baseFilter);
      setAttendantsData(response.results);
    };

    const fetchTags = async () => {
      const baseFilter = toHelpdeskBaseFilter('tickets_panel_tags', selectedFilter, tenantId);
      const response = await apiBlipInsightsService.getData(baseFilter);
      const updatedData = response.results.slice(1); // TODO: remove this when no tags behavior is set
      setTagsData(updatedData);
    };

    fetchData();
  }, [view, selectedFilter, tenantId, apiBlipInsightsService, attendantsData, tagsData]);

  useEffect(() => {
    if (buttonGroupRef.current) buttonGroupRef.current.activateButton(0);
    setQueueData(data);
    setAttendantsData(null);
    setTagsData(null);
    setView('queues');
  }, [selectedFilter, data]);

  return (
    <div className="table-tickets-panel">
      <TemplateTable
        title={translations.blip_insights_tickets_panel.title}
        description={translations.blip_insights_tickets_panel.description}
        heading={heading}
        tableData={tableData}
        sortedColumn={sortedColumn}
        arrow={arrow}
        toggleSort={toggleSort}
        isLoading={isLoading}
        isScrollable={isScrollable}
        view={view}
      >
        <BdsButtonGroup ref={buttonGroupRef} color="content" direction="row" size="medium">
          <BdsButton onClick={() => setView('queues')}>
            {translations.blip_insights_tickets_panel.buttonQueues}
          </BdsButton>
          <BdsButton onClick={() => setView('attendants')}>
            {translations.blip_insights_tickets_panel.buttonAttendants}
          </BdsButton>
          <BdsButton onClick={() => setView('tags')}>{translations.blip_insights_tickets_panel.buttonTags}</BdsButton>
        </BdsButtonGroup>
      </TemplateTable>
    </div>
  );
};
