import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { processTableData, sortTableData } from '@utils/tableUtils';
import TemplateTable from '../template_table';
import './users_response.scss';

interface TableUsersResponseProps {
  data: Array<Array<string | number | null>>;
}

export const TableUsersResponse: React.FC<TableUsersResponseProps> = ({
  data,
}) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  const [usersResponseData, setUsersResponseData] = useState(data);
  const [sortedColumn, setSortedColumn] = useState<number | null>(null);
  const [sortDirection, setSortDirection] = useState(1);
  const [arrow, setArrow] = useState('');

  const heading = [
    translations.blip_insights_users_response.headings.responseText,
    translations.blip_insights_users_response.headings.totalResponses,
    translations.blip_insights_users_response.headings.responseRate,
    translations.blip_insights_users_response.headings.averageResponseTime,
  ];

  const columnTypes = useMemo(() => [
    'string',
    'number',
    'percentage',
    'time',
  ], []);

  useEffect(() => {
    const processedData = processTableData(data, columnTypes);
    setUsersResponseData(processedData);
  }, [data, columnTypes]);

  const isScrollable = useMemo(() => {
    return Array.isArray(usersResponseData) && usersResponseData.length > 6;
  }, [usersResponseData]);

  const tableData = useMemo(() => {
    if (sortedColumn === null) return usersResponseData;
    return sortTableData(usersResponseData, columnTypes, sortedColumn, sortDirection);
  }, [sortedColumn, usersResponseData, columnTypes, sortDirection]);

  const toggleSort = (columnIndex: number) => {
    setSortedColumn(prevColumn => {
      const isSameColumn = prevColumn === columnIndex;
      const newDirection = isSameColumn ? -sortDirection : 1;
      setSortDirection(newDirection);
      setArrow(newDirection === 1 ? 'asc' : 'dsc');
      return columnIndex;
    });
  };

  return (
    <div className="table-users-response">
      <TemplateTable
        title={translations.blip_insights_users_response.title}
        description={translations.blip_insights_users_response.description}
        heading={heading}
        tableData={tableData}
        sortedColumn={sortedColumn}
        arrow={arrow}
        toggleSort={toggleSort}
        isScrollable={isScrollable}
      />
    </div>    
  );
};
