import React from 'react';
import {
  BdsTable,
  BdsTable<PERSON>eader,
  BdsTableRow,
  BdsTableTh,
  BdsTableBody,
  BdsTableCell,
} from 'blip-ds/dist/blip-ds-react/components';
import './template_table.scss';

interface TemplateTableProps {
  title: string;
  description: string;
  heading: string[];
  tableData: Array<Array<string | number | null>>;
  sortedColumn: number | null;
  arrow: string;
  toggleSort: (columnIndex: number) => void;
  isLoading?: boolean;
  isScrollable?: boolean;
  children?: React.ReactNode;
  view?: string;
}

const TemplateTable: React.FC<TemplateTableProps> = ({
  title,
  description,
  heading,
  tableData,
  sortedColumn,
  arrow,
  toggleSort,
  isLoading = false,
  isScrollable = false,
  children,
  view,
}) => {
  const hasData = tableData && tableData.length > 0;

  return (
    <div className="template-table">
      <div className="template-table__header">
        <div>
          <h4 className="template-table__title">{title}</h4>
          <p className="template-table__description">{description}</p>
        </div>
        {children && <div className="template-table__extra">{children}</div>}
      </div>
      <div className={`template-table__container ${isScrollable ? 'scrollable' : ''}`}>
        {isLoading ? (
          <p>Carregando...</p>
        ) : hasData ? (
          <BdsTable class="template-table__content" key={view || undefined}>
            <BdsTableHeader class="template-table__header-table">
              <BdsTableRow>
                {heading.map((item, index) => {
                  return (
                    <BdsTableTh
                      key={index}
                      onClick={() => toggleSort(index)}
                      sortable={sortedColumn === index ? true : false}
                      arrow={arrow}
                      class="template-table__th"
                    >
                      {item}
                    </BdsTableTh>
                  );
                })}
              </BdsTableRow>
            </BdsTableHeader>
            <BdsTableBody class="template-table__body-table">
              {tableData.map((row, rowIndex) => (
                <BdsTableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <BdsTableCell class="template-table__cell" key={cellIndex}>
                      <bds-typo variant="fs-14" bold={sortedColumn === cellIndex ? 'bold' : 'regular'}>
                        {cell}
                      </bds-typo>
                    </BdsTableCell>
                  ))}
                </BdsTableRow>
              ))}
            </BdsTableBody>
          </BdsTable>
        ) : (
          <div className="template-table__no-data">
            <bds-typo variant="fs-16" bold="bold">
              Não há dados para o filtro selecionado
            </bds-typo>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateTable;
