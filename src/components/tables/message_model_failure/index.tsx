import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { processTableData, sortTableData } from '@utils/tableUtils';
import TemplateTable from '../template_table';
import './message_model_failure.scss';

interface TableMessageModelFailureProps {
  data: Array<Array<string | number | null>>;
}

export const TableMessageModelFailure: React.FC<TableMessageModelFailureProps> = ({
  data,
}) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  const [messageModelFailureData, setMessageModelFailureData] = useState(data);
  const [sortedColumn, setSortedColumn] = useState<number | null>(null);
  const [sortDirection, setSortDirection] = useState(1);
  const [arrow, setArrow] = useState('');

  const heading = [
    translations.message_model_failure.headings.failureType,
    translations.message_model_failure.headings.totalFailures,
    translations.message_model_failure.headings.failureRate,
  ];

  const columnTypes = useMemo(() => [
    'string',
    'number',
    'percentage',
  ], []);

  useEffect(() => {
    const processedData = processTableData(data, columnTypes);
    setMessageModelFailureData(processedData);
  }, [data, columnTypes]);

  const isScrollable = useMemo(() => {
    return Array.isArray(messageModelFailureData) && messageModelFailureData.length > 6;
  }, [messageModelFailureData]);

  const tableData = useMemo(() => {
    if (sortedColumn === null) return messageModelFailureData;
    return sortTableData(messageModelFailureData, columnTypes, sortedColumn, sortDirection);
  }, [sortedColumn, messageModelFailureData, columnTypes, sortDirection]);

  const toggleSort = (columnIndex: number) => {
    setSortedColumn(prevColumn => {
      const isSameColumn = prevColumn === columnIndex;
      const newDirection = isSameColumn ? -sortDirection : 1;
      setSortDirection(newDirection);
      setArrow(newDirection === 1 ? 'asc' : 'dsc');
      return columnIndex;
    });
  };

  return (
    <div className="table-message-model-failure">
      <TemplateTable
        title={translations.message_model_failure.title}
        description={translations.message_model_failure.description}
        heading={heading}
        tableData={tableData}
        sortedColumn={sortedColumn}
        arrow={arrow}
        toggleSort={toggleSort}
        isScrollable={isScrollable}
      />
    </div>
  );
};
