import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';
import { processTableData, sortTableData } from '@utils/tableUtils';
import TemplateTable from '../template_table';
import './active_message_performance.scss';
import { BdsAutocomplete } from 'blip-ds/dist/blip-ds-react/components';
import { BdsAutocompleteCustomEvent } from 'blip-ds';
import { AutocompleteMultiSelectedChangeEventDetail } from 'blip-ds/dist/types/components/autocomplete/autocomplete-select-interface';

interface ActiveMessagePerformanceProps {
  data: Array<Array<string | number | null>>;
}

export const TableActiveMessagePerformance: React.FC<ActiveMessagePerformanceProps> = ({
  data,
}) => {
  const { translate } = useTranslation();
  const translations = translate(translation);

  const [activeMessagePerformanceData, setActiveMessagePerformanceData] = useState(data);
  const [sortedColumn, setSortedColumn] = useState<number | null>(null);
  const [sortDirection, setSortDirection] = useState(1);
  const [arrow, setArrow] = useState('');
  const [selectedModels, setSelectedModels] = useState<string[]>([]);

  const heading = [
    translations.active_message_performance.headings.modelName,
    translations.active_message_performance.headings.sentMessages,
    translations.active_message_performance.headings.deliveryRate,
    translations.active_message_performance.headings.readRate,
    translations.active_message_performance.headings.responseRate,
    translations.active_message_performance.headings.failureRate,
  ];

  const columnTypes = useMemo(() => [
    'string',
    'number',
    'percentage',
    'percentage',
    'percentage',
    'percentage',
  ], []);

  useEffect(() => {
    const processedData = processTableData(data, columnTypes);
    setActiveMessagePerformanceData(processedData);
  }, [data, columnTypes]);

  const isScrollable = useMemo(() => {
    return Array.isArray(activeMessagePerformanceData) && activeMessagePerformanceData.length > 6;
  }, [activeMessagePerformanceData]);

  const handleModelSelection = (
    event: BdsAutocompleteCustomEvent<AutocompleteMultiSelectedChangeEventDetail>
  ) => {
    setSelectedModels((event.detail.value || []).map(option => option.value));
  };

  const filteredData = useMemo(() => {
    if (selectedModels.length === 0) return activeMessagePerformanceData;
    return activeMessagePerformanceData.filter(row => selectedModels.includes(row[0] as string));
  }, [selectedModels, activeMessagePerformanceData]);

  const tableData = useMemo(() => {
    if (sortedColumn === null) return filteredData;
    return sortTableData(filteredData, columnTypes, sortedColumn, sortDirection);
  }, [sortedColumn, filteredData, columnTypes, sortDirection]);

  const toggleSort = (columnIndex: number) => {
    setSortedColumn(prevColumn => {
      const isSameColumn = prevColumn === columnIndex;
      const newDirection = isSameColumn ? -sortDirection : 1;
      setSortDirection(newDirection);
      setArrow(newDirection === 1 ? 'asc' : 'dsc');
      return columnIndex;
    });
  };

  return (
    <div className="table-active-message-performance">
      <TemplateTable
        title={translations.active_message_performance.title}
        description={translations.active_message_performance.description}
        heading={heading}
        tableData={tableData}
        sortedColumn={sortedColumn}
        arrow={arrow}
        toggleSort={toggleSort}
        isScrollable={isScrollable}
      >
        <BdsAutocomplete
          class="table-active-message-performance__autocomplete"
          selectionType="multiple"
          placeholder={translations.active_message_performance.autocomplete.placeholder}
          label={translations.active_message_performance.autocomplete.label}
          options={activeMessagePerformanceData.map(row => ({
            label: row[0] as string,
            value: row[0] as string,
          }))}
          optionsPosition="bottom"
          onBdsMultiselectedChange={handleModelSelection}
        />
      </TemplateTable>
      
    </div>
  );
};
