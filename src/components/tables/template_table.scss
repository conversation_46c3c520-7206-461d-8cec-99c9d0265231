@import 'blip-ds/dist/collection/styles/_colors.scss';

@mixin dynamic-column-widths($columns: 4) {
  &:first-child {
    width: calc(100% / (#{$columns} + 1) * 2);
  }

  &:not(:first-child) {
    width: calc(100% / (#{$columns} + 1));
  }
}

.template-table {
  display: flex;
  flex-direction: column;
  padding: var(--2, 16px);
  gap: var(--2, 16px);
  align-items: flex-start;
  align-self: stretch;
  border-radius: var(--2, 16px);
  background: var(--color-surface-1, #f6f6f6);
  width: 100%;
  max-height: 620px;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-height: 50px;
  }

  &__title {
    color: var(--content-color-content-default, var(--color-content-default, #454545));
    font-family: 'Nunito Sans';
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
    margin: 0;
  }

  &__description {
    color: var(--color-content-disable, #636363);
    font-family: 'Nunito Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin: 0;
  }

  &__container {
    width: 100%;
    overflow-y: hidden;
    overflow-x: auto;

    &.scrollable {
      overflow-y: auto;
    }
  }

  &__content {
    table-layout: fixed;
    width: 100%;
    margin: 0;
    padding: 0;
    border: none;
  }

  &__header-table {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--color-surface-1, #f6f6f6);
  }

  &__cell {
    @include dynamic-column-widths();
  }

  &__th {
    @include dynamic-column-widths();
  }
}
