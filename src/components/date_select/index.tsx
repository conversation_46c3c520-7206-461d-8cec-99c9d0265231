import React from 'react';
import { BdsDatepicker } from 'blip-ds/dist/blip-ds-react/components';
import { formatToBrazilianDate } from '@utils/date';
import './date_select.scss';

export interface DateSelectProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (e: CustomEvent<any>) => void;
  onEndDateChange: (e: CustomEvent<any>) => void;
  onConcludeDateClick?: (e: CustomEvent<any>) => void;
}

const DateSelect = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onConcludeDateClick,
}: DateSelectProps) => {
  const startDateLimit = (() => {
    const today = new Date();
    today.setFullYear(today.getFullYear() - 1);
    return formatToBrazilianDate(today);
  })();
  const initialStartDate = (() => {
    const today = new Date();
    today.setDate(today.getDate() - 7);
    return formatToBrazilianDate(today);
  })();
  const endDateLimit = (() => {
    const today = new Date();
    return formatToBrazilianDate(today);
  })();
  const initialEndDate = (() => {
    const today = new Date();
    return formatToBrazilianDate(today);
  })();

  const valueStartDateSelected = !!startDate && startDate !== '' ? startDate : initialStartDate;
  const valueEndDateSelected = !!endDate && endDate !== '' ? endDate : initialEndDate;

  return (
    <div className="date_select">
      <BdsDatepicker
        class="date_select__datepicker"
        startDateLimit={startDateLimit}
        endDateLimit={endDateLimit}
        typeOfDate="period"
        positionOptions="bottom-center"
        onBdsEndDate={onEndDateChange}
        onBdsStartDate={onStartDateChange}
        onConcludeDatepicker={onConcludeDateClick}
        valueDateSelected={valueStartDateSelected}
        valueEndDateSelected={valueEndDateSelected}
      />
    </div>
  );
};

export default DateSelect;
