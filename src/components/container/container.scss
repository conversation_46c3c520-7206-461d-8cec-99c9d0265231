@import '~blip-ds/dist/collection/styles/_colors.scss';

$breakpoint-1366: 1366px;
$breakpoint-1920: 1920px;

@mixin blip-insights-container-padding {
  --container-padding-horizontal: var(--5, 40px);
  padding: var(--0, 0px) var(--container-padding-horizontal);

  @media (min-width: $breakpoint-1366) {
    --container-padding-horizontal: var(--8, 64px);
    padding: var(--0, 0px) var(--container-padding-horizontal);
  }

  @media (min-width: $breakpoint-1920) {
    --container-padding-horizontal: 248px;
    padding: var(--0, 0px) var(--container-padding-horizontal);
  }
}

.blip-insights-container {
  min-height: 100vh;
  position: relative;
  @include blip-insights-container-padding;

  &__tab-group {
    margin-left: -16px;
    overflow-y: visible;
    min-height: 100%;
  }

  &__items {
    margin-left: 16px;
  }

  &__update-button {
    position: absolute;
    top: var(--1, 8px);
    right: var(--container-padding-horizontal);
  }

  &__no-update-toast {
    position: fixed;
    bottom: 48px;
    right: 48px;
    z-index: 1000;
  }
}
