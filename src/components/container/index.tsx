import React, { useEffect, useRef, useCallback } from 'react';
import { useTranslation } from '@hooks/useTranslation';
import { translation } from './i18n/translation';

import { Overview } from '@pages/overview';
import { Helpdesks } from '@pages/helpdesks';
import { ProactiveMessages } from '@pages/proactiveMessages';

import './container.scss';
import { TAB_NAMES, useTabContext } from '../../contexts/TabsContext';
import {
  finishedFetchLastDataUpdateEventName,
  finishedOnApplyFilterEventName,
  startedOnApplyFilterEventName,
  updateClickedEventName,
} from '@utils/constants';
import { BdsToast } from 'blip-ds/dist/blip-ds-react/components';
import { useAppContext } from '@contexts/AppContext';
import TemplatesComparing from '@pages/templatesComparing';
import { TemplateProvider } from '@contexts/TemplateContext';
import Datamind from '@pages/datamind';
import { BLIP_INSIGHTS_FEATURE_TOGGLES } from '@services/FeatureToggleService';

export const Container = () => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const { activeTab, setActiveTab } = useTabContext();
  const tabGroupRef = useRef<HTMLBdsTabGroupElement>(null);
  const { showToast, getFeatureToggleService } = useAppContext();
  const [disableUpdateButton, setDisableUpdateButton] = React.useState(true);
  const [dataMindEnabled, setDataMindEnabled] = React.useState<boolean>(false);

  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab);
    },
    [setActiveTab]
  );

  const setRefreshButtonDisabled = useCallback(() => {
    setDisableUpdateButton(true);
  }, []);

  const setRefreshButtonEnabled = useCallback(() => {
    setDisableUpdateButton(false);
  }, []);

  useEffect(() => {
    document.removeEventListener(startedOnApplyFilterEventName, setRefreshButtonDisabled);
    document.removeEventListener(finishedFetchLastDataUpdateEventName, setRefreshButtonEnabled);
    document.removeEventListener(finishedOnApplyFilterEventName, setRefreshButtonEnabled);
    document.addEventListener(startedOnApplyFilterEventName, setRefreshButtonDisabled);
    document.addEventListener(finishedFetchLastDataUpdateEventName, setRefreshButtonEnabled);
    document.addEventListener(finishedOnApplyFilterEventName, setRefreshButtonEnabled);

    return () => {
      document.removeEventListener(startedOnApplyFilterEventName, setRefreshButtonDisabled);
      document.removeEventListener(finishedFetchLastDataUpdateEventName, setRefreshButtonEnabled);
      document.removeEventListener(finishedOnApplyFilterEventName, setRefreshButtonEnabled);
    };
  }, [setRefreshButtonDisabled, setRefreshButtonEnabled]);

  useEffect(() => {
    const checkFeatureToggle = async () => {
      const featureToggleService = await getFeatureToggleService();
      const isDatamindEnabled = await featureToggleService.isUserFeatureEnabled(
        BLIP_INSIGHTS_FEATURE_TOGGLES.DATA_MIND_VISIBILITY,
        false
      );
      console.log('isDatamindEnabled', isDatamindEnabled);
      setDataMindEnabled(isDatamindEnabled);
    };
    checkFeatureToggle();
  }, [getFeatureToggleService, setActiveTab]);

  useEffect(() => {
    if (tabGroupRef.current) {
      const tabs = document.getElementById('insights-tabs');
      tabs &&
        tabs.addEventListener('bdsTabChange', (e: Event) => {
          const customEvent = e as CustomEvent<{ id: string }>;
          if (customEvent.detail && customEvent.detail.id) {
            handleTabChange(customEvent.detail.id);
          }
        });
    }
  }, [tabGroupRef, handleTabChange]);

  const onUpdateClick = useCallback(() => {
    if (!disableUpdateButton) {
      setDisableUpdateButton(true);
      document.dispatchEvent(new CustomEvent(updateClickedEventName));
    }
  }, [setDisableUpdateButton, disableUpdateButton]);

  return (
    <bds-grid class="blip-insights-container" direction="column" gap="3">
      <bds-tab-group class="blip-insights-container__tab-group" align="left" id="insights-tabs" ref={tabGroupRef}>
        <div className="blip-insights-container__items">
          <bds-tab-item label={translations.blip_insights_menu_bar.overview} id="overview">
            {activeTab === TAB_NAMES.overview && <Overview />}
          </bds-tab-item>
          <bds-tab-item label={translations.blip_insights_menu_bar.helpdesks} id="helpdesks">
            {activeTab === TAB_NAMES.helpdesks && <Helpdesks />}
          </bds-tab-item>
          <bds-tab-item label={translations.blip_insights_menu_bar.proactiveMessages} id="proactive-messages">
            <TemplateProvider>
              {activeTab === TAB_NAMES.proactiveMessages && <ProactiveMessages />}
              {activeTab === TAB_NAMES.proactiveMessagesTemplatesComparing && <TemplatesComparing />}
            </TemplateProvider>
          </bds-tab-item>
          {dataMindEnabled && (
            <bds-tab-item label={translations.blip_insights_menu_bar.datamind} id="datamind">
              {activeTab === TAB_NAMES.datamind && <Datamind />}
            </bds-tab-item>
          )}
        </div>
      </bds-tab-group>
      {activeTab !== TAB_NAMES.proactiveMessagesTemplatesComparing && (
        <bds-button
          class="blip-insights-container__update-button"
          onClick={onUpdateClick}
          disabled={disableUpdateButton}
          color="content"
          variant="text"
          icon-left="refresh"
          size="medium"
        >
          {translations.blip_insights_button_update}
        </bds-button>
      )}
      <BdsToast
        class="blip-insights-container__no-update-toast"
        icon="bell"
        toastTitle={translations.noUpdateToast.title}
        toastText={translations.noUpdateToast.text}
        variant="system"
        show={showToast}
      />
    </bds-grid>
  );
};
