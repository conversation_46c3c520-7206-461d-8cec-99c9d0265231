.funnel-indicators-container {
  min-width: 30%;
  height: 40px;
  border-radius: 10px;
  padding-top: 8px;
  padding-right: 8px;
  padding-bottom: 8px;
  padding-left: 8px;
  background: var(--color-surface-2, rgba(224, 224, 224, 1));
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;

  &__comparative {
    min-width: 30%;
    gap: 4px;
    height: 40px;
    border-radius: 10px;
    padding-top: 8px;
    padding-right: 8px;
    padding-bottom: 8px;
    padding-left: 8px;
    background: var(--color-surface-2, rgb(224, 224, 224));
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
  }
}

.funnel-indicators-status {
  color: var(--color-content-default, rgb(69, 69, 69));
  line-height: 22px;
  text-align: center;
  margin: 0;
  min-width: 45%;

  p {
    font-size: 12px;
    margin-bottom: 0;
    font-weight: 600;
  }

  &__no-conversion {
    color: var(--color-content-default, rgba(69, 69, 69, 1));
    font-family: Nunito Sans;
    font-weight: 400;
    font-style: italic;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0%;
    vertical-align: middle;
    text-wrap: nowrap;
  }
}
.funnel-indicators-variation {
  display: contents;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  p {
    font-size: 12px;
    margin-bottom: 0;
    min-width: 20%;
    text-align: center;
  }

  &__comparative {
    display: contents;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    p {
      min-width: 30%;
      font-size: 12px;
      margin-bottom: 0;
      text-align: center;
    }
  }
}
