import React from 'react';
import './funnel_item_indicator.scss';
import { FunnelChartType, IndicatorData } from '@typings/FunnelChartData';

const translation = {
  pt: {
    no_conversion: 'Sem eventos de conversão configurados',
    no_conversion__comparative: 'Não configurado',
    label: {
      sent: 'falha',
      received: 'recebimento',
      read: 'leitura',
      responded: 'resposta',
      conversion: 'conversão',
      default: 'envio',
    },
  },
};
const NA_STRING = 'N/A'; // String to represent Not Available
const getChipColor = (variation: string | number, inverted: boolean) => {
  if (!variation || variation === 'null') return 'disabled';
  const variationStr = typeof variation !== 'string' ? variation.toString() : variation;
  if (variationStr.trim().startsWith('-')) return !inverted ? 'danger' : 'success';
  const varationInNumber = parseInt(variationStr.replace(/\D/g, ''));
  if (variationStr === NA_STRING || varationInNumber === 0) return 'disabled';
  return !inverted ? 'success' : 'danger';
};

interface FunnelItemIndicatorsProps {
  item: IndicatorData;
  comparative?: boolean;
  type?: FunnelChartType;
}

const FunnelItemIndicators = ({ item, comparative, type }: FunnelItemIndicatorsProps) => {
  const { status, variation, revertVariationChipColor } = item;
  const isMiniminum = status && parseFloat(status) > 0;

  if (comparative) {
    return isMiniminum ? (
      <div className="funnel-indicators-container__comparative">
        <div className="funnel-indicators-variation__comparative">
          <bds-chip-tag color="disabled">{parseFloat(status).toFixed(1)}%</bds-chip-tag>
          {type && <p>{translation.pt.label[type ?? 'default']}</p>}
        </div>
      </div>
    ) : (
      <div className="funnel-indicators-container">
        <div className="funnel-indicators-status__no-conversion">{translation.pt.no_conversion__comparative}</div>
      </div>
    );
  }

  return isMiniminum ? (
    <div className="funnel-indicators-container">
      {type && (
        <div className="funnel-indicators-status">
          <p>{`${parseFloat(status).toFixed(1)}% de ${translation.pt.label[type ?? 'default']}`}</p>
        </div>
      )}
      <div className="funnel-indicators-variation">
        <bds-chip-tag color={getChipColor(variation, revertVariationChipColor)}>{variation && variation !== 'null' && variation.toString().trim().length ? `${parseFloat(variation).toFixed(1)}%` : NA_STRING}</bds-chip-tag>
        {item.label && <p>no período</p>}
      </div>
    </div>
  ) : (
    <div className="funnel-indicators-container">
      <div className="funnel-indicators-status__no-conversion">{translation.pt.no_conversion}</div>
    </div>
  );
};

export default FunnelItemIndicators;
