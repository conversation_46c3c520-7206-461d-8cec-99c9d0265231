import React from 'react';

interface FunnelCornerSvgProps {
  orientation: 'left' | 'right';
  color?: string;
  width?: number;
  height?: number;
}

const FunnelCornerSvg: React.FC<FunnelCornerSvgProps> = ({ color = '#1968F0', width = 24, height = 55, orientation }) => {
  return orientation === 'left' ? (
    <svg width={width} height={height} viewBox="0 0 24 55" fill="#0066ff" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M24 55V0H7.81652C2.39697 1.19637e-05 -1.37515 5.51691 0.476942 10.7344L13.8539 48.417C15.2567 52.3689 18.9236 55 23.0286 55H24Z"
        fill={color}
      />
    </svg>
  ) : (
    <svg width={width} height={height} viewBox="0 0 24 55" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 55V0H16.1835C21.603 1.19637e-05 25.3752 5.51691 23.5231 10.7344L10.1461 48.417C8.74329 52.3689 5.07639 55 0.971428 55H0Z"
        fill={color}
      />
    </svg>
  );
};

export default FunnelCornerSvg;
