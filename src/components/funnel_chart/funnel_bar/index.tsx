import React from 'react';
import './funnel_bar.scss';
import FunnelCornerSvg from './funnel_corner_svg';
import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { FunnelChartType } from '@typings/FunnelChartData';

export interface FunnelBarProps {
  bars: {
    value: number;
    tooltip: string;
  };
  maxValue: number;
  color?: string;
  minColor?: string;
  type?: FunnelChartType;
}

const FunnelBar = ({ bars, maxValue, color = '', minColor = '', type = 'read'}: FunnelBarProps) => {
  const DEFAULT_FILL_COLOR = color || '#1968F0';
  const MINIMUM_FILL_COLOR = minColor || '#B2DFFD';

  // Calculate width percentage
  const MINIMUM = 4;
  const widthPercentage = (value: number) => Math.round((value / maxValue) * 100);
  const funnelWidth = widthPercentage(bars.value);

  return (
    <div className="funnel-bar-container" style={{ width: `${Math.max(funnelWidth, MINIMUM)}%` }}>
      {bars.value < MINIMUM ? (
        <>
          <div className="funnel-bar-edge funnel-bar-edge-left">
            <FunnelCornerSvg orientation="left" color={MINIMUM_FILL_COLOR} />
          </div>
          <div className="funnel-bar-value" style={{ color: '#141414' }}>
            {formatNumberForChart(Number(bars.value))}
          </div>
          <div className="funnel-bar-edge funnel-bar-edge-right">
            <FunnelCornerSvg orientation="right" color={MINIMUM_FILL_COLOR} />
          </div>
        </>
      ) : (
        <>
          <div className="funnel-bar-edge funnel-bar-edge-left">
            <FunnelCornerSvg orientation="left" color={DEFAULT_FILL_COLOR} />
          </div>
          <div className="funnel-bar-content">
            <div
              className="funnel-bar"
              style={{
                backgroundColor: DEFAULT_FILL_COLOR,
              }}
            ></div>
            <div
              className="funnel-bar-value"
              style={{
                color: 'white',
              }}
            >
              <bds-tooltip tooltip-text={bars?.tooltip?.concat('% do total')} position={'right-center'}>
                {formatNumberForChart(Number(bars.value))}
              </bds-tooltip>
            </div>
          </div>
          <div className="funnel-bar-edge funnel-bar-edge-right">
            <FunnelCornerSvg orientation="right" color={DEFAULT_FILL_COLOR} />
          </div>
        </>
      )}
    </div>
  );
};

export default FunnelBar;
