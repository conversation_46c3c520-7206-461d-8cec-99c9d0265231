import React, { useCallback } from 'react';
import './funnel_chart.scss';
import FunnelBar from './funnel_bar';
import FunnelItemIndicators from './funnel_item_indicators';
import { translation as translationFromFile } from './i18n/translations';

import { FunnelChartData, FunnelChartType } from '@typings/FunnelChartData';
import { TAB_NAMES, useTabContext } from '@contexts/TabsContext';
import { useTranslation } from '@hooks/useTranslation';
import ConversionBanner from '@components/minor_components/conversion-banner/conversion_banner';
import { BdsAutocomplete } from 'blip-ds/dist/blip-ds-react/components';

export interface FunnelItem {
  bars: {
    value: number;
    tooltip: string;
  };
  indicators: {
    status: string;
    variation: string;
    revertVariationChipColor: boolean;
  };
  label: string;
  color?: string;
  statusColor?: string;
}

export interface FunnelChartProps {
  title?: string;
  description?: string;
  datasets: FunnelChartData;
  compare?: boolean;
  banner?: boolean;
  select?: any;
  selectOnChange?: (value: any) => void;
  selectedOption?: string;
  barsColor?: string;
  minColor?: string;
  showCompareButton?: boolean;
}

interface FunnelStageProps {
  stageKey: FunnelChartType;
  labelText: string;
  data: {
    bars: {
      value: number;
      tooltip: string;
    };
    indicators: {
      status: string;
      variation: string;
      revertVariationChipColor: boolean;
      label?: string;
    };
  };
  maxValue: number;
  compare?: boolean;
  barsColor?: string;
  minColor?: string;
}

const FunnelStage: React.FC<FunnelStageProps> = ({
  stageKey,
  labelText,
  data,
  maxValue,
  compare,
  barsColor,
  minColor,
}) => {
  const stageClass = compare ? 'funnel-stage__comparative' : 'funnel-stage';
  const labelClass = compare ? 'funnel-label__comparative' : 'funnel-label';

  return (
    <div className={stageClass}>
      <div className={labelClass}>{labelText}</div>
      <FunnelBar color={barsColor} minColor={minColor} bars={data.bars} maxValue={maxValue} type={stageKey} />
      <FunnelItemIndicators item={data.indicators} comparative={compare} type={stageKey} />
    </div>
  );
};

interface ChartHeaderProps {
  title: string;
  description: string;
  select?: any;
  selectOnChange?: (value: any) => void;
  selectedOption?: string;
  showCompareButton?: boolean;
  loadCompareTemplatesScreen: () => void;
  translations: Record<string, string>;
}

const ChartHeader: React.FC<ChartHeaderProps> = ({
  title,
  description,
  select,
  selectOnChange,
  selectedOption,
  showCompareButton,
  loadCompareTemplatesScreen,
  translations,
}) => (
  <div className="funnel-chart-header">
    <div className="funnel-chart-header--left">
      <div className="funnel-chart-title">{title}</div>
      <div className="funnel-chart-subtitle">{description}</div>
    </div>

    {select && (
      <BdsAutocomplete
        class="funnel-chart__autocomplete"
        selectionType="single"
        placeholder={translations.compare_select_placeholder}
        label={translations.compare_select_label}
        options={select}
        optionsPosition="bottom"
        onBdsSelectedChange={(option) => {
          selectOnChange && selectOnChange(option.detail.value);
        }}
        value={selectedOption}
      />
    )}

    {showCompareButton && (
      <div className="funnel-chart-header--right">
        <bds-button
          class="funnel-chart-container__templates-comparing-button"
          onClick={loadCompareTemplatesScreen}
          color="content"
          variant="primary"
          icon-right="trophy"
          size="medium"
        >
          {translations.compare_button_label}
        </bds-button>
      </div>
    )}
  </div>
);

interface FunnelContentProps {
  datasets: FunnelChartData;
  compare?: boolean;
  translations: Record<string, string>;
  barsColor?: string;
  minColor?: string;
}

const FunnelContent: React.FC<FunnelContentProps> = ({ datasets, compare, translations, barsColor, minColor }) => {
  const chartClass = compare ? 'funnel-chart__comparative' : 'funnel-chart';

  const stages: Array<{
    key: FunnelChartType;
    label: string;
    maxValue: number;
    useMinColor?: boolean;
  }> = [
    {
      key: 'sent',
      label: translations.funnel_chart_label_sent,
      maxValue: datasets.sent.bars.value,
    },
    {
      key: 'received',
      label: translations.funnel_chart_label_received,
      maxValue: datasets.sent.bars.value,
    },
    {
      key: 'read',
      label: translations.funnel_chart_label_read,
      maxValue: datasets.received.bars.value,
    },
    {
      key: 'responded',
      label: translations.funnel_chart_label_responded,
      maxValue: datasets.received.bars.value,
    },
    {
      key: 'conversion',
      label: translations.funnel_chart_label_conversion,
      maxValue: datasets.received.bars.value,
      useMinColor: true,
    },
  ];
  let index = 0;

  return (
    <div className={chartClass}>
      {stages.map((stage) => (
        <FunnelStage
          key={index++}
          stageKey={stage.key}
          labelText={stage.label}
          data={datasets[stage.key as keyof FunnelChartData]}
          maxValue={stage.maxValue}
          compare={compare}
          barsColor={barsColor}
          minColor={stage.useMinColor ? minColor : ''}
        />
      ))}
    </div>
  );
};

const NoDataView: React.FC<{ message?: string }> = ({ message }) => (
  <div className="funnel-chart__no-data">
    <bds-typo variant="fs-16" bold="bold">
      {message}
    </bds-typo>
  </div>
);

const FunnelChart: React.FC<FunnelChartProps> = ({
  datasets,
  showCompareButton = false,
  compare = false,
  banner = true,
  select = undefined,
  selectOnChange = () => null,
  selectedOption = '',
  barsColor = '#1968F0',
  minColor = '#B2DFFD',
  title = '',
  description = '',
}) => {
  const { translate } = useTranslation();
  const translations = translate(translationFromFile);
  const { setActiveTab } = useTabContext();

  const chartTitle = title || translations.title;
  const chartDescription = description || translations.subtitle;

  const loadCompareTemplatesScreen = useCallback(() => {
    setActiveTab(TAB_NAMES.proactiveMessagesTemplatesComparing);
  }, [setActiveTab]);

  const hasData = datasets && Object.values(datasets).some((item) => item.bars && item.bars.value > 0);

  return (
    <div className="funnel-chart-container">
      <ChartHeader
        title={chartTitle}
        description={chartDescription}
        select={select}
        selectOnChange={selectOnChange}
        selectedOption={selectedOption}
        showCompareButton={showCompareButton}
        loadCompareTemplatesScreen={loadCompareTemplatesScreen}
        translations={translations}
      />
      {hasData ? (
        <>
          <FunnelContent
            datasets={datasets}
            compare={compare}
            translations={translations}
            barsColor={barsColor}
            minColor={minColor}
          />
          {banner && <ConversionBanner />}
        </>
      ) : (
        <NoDataView message={translations.no_data_message} />
      )}
    </div>
  );
};

export default FunnelChart;
