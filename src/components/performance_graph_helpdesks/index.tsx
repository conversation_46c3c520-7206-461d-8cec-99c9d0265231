import React from 'react';
import { translation } from './i18n/translation';
import { useTranslation } from '@hooks/useTranslation';
import LineBarChart from '@components/line_bar_chart';
import { getDatasets, options } from './graph_settings';

type Avaragetime = {
  [key: string]: number[];
};

type SelectOption = {
  [key: string]: string;
};

interface PerformanceGraphProps {
  labelPeriods: string[];
  selectOptions: SelectOption;
  openTicketsData: number[];
  closedTicketsData: number[];
  avarageTime: Avaragetime;
}

const PerformanceGraphHelpdesks = ({
  labelPeriods,
  selectOptions,
  openTicketsData,
  closedTicketsData,
  avarageTime,
}: PerformanceGraphProps) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const datasets = getDatasets({
    selectOptions,
    avarageTime,
    openTicketsData,
    closedTicketsData,
  });

  return (
    <LineBarChart
      title={translations.blip_insights_performance_graph.title}
      description={translations.blip_insights_performance_graph.subtitle}
      labelPeriods={labelPeriods}
      selectOptions={selectOptions}
      datasets={datasets}
      defaultSelectedOption="TMA"
      chartOptions={options}
      hasSelect={true}
    />
  );
};

export default PerformanceGraphHelpdesks;
