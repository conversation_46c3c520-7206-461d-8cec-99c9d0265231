import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { Chart } from 'chart.js';

type Avaragetime = {
  [key: string]: number[];
};

type SelectOption = {
  [key: string]: string;
};

interface DatasetConfigProps {
  selectOptions: SelectOption;
  avarageTime: Avaragetime;
  openTicketsData: number[];
  closedTicketsData: number[];
}

export const getDatasets = ({ selectOptions, avarageTime, openTicketsData, closedTicketsData }: DatasetConfigProps) => [
  {
    type: 'line' as const,
    label: selectOptions,
    borderColor: 'rgba(240, 99, 5, 1)',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 99, 5, 1)',
    fill: true,
    data: avarageTime,
    yAxisID: 'y1',
  },
  {
    type: 'bar' as const,
    label: 'Tickets Abertos',
    backgroundColor: 'rgba(25, 104, 240, 0.65)',
    data: openTicketsData,
    borderColor: 'rgba(25, 104, 240, 1)',
    borderWidth: 2,
    borderRadius: 10,
  },
  {
    type: 'bar' as const,
    label: 'Tickets Fechados',
    backgroundColor: 'rgba(253, 155, 220, 0.65)',
    data: closedTicketsData,
    borderColor: 'rgba(253, 155, 220, 1)',
    borderWidth: 2,
    borderRadius: 10,
  },
];

export const options: any = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        generateLabels: (chart: Chart) => {
          return chart.data.datasets.map((dataset) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            lineWidth: dataset.type === 'line' ? 2 : 0,
            strokeStyle: dataset.borderColor || 'black',
            pointStyle: dataset.type === 'line' ? 'dash' : 'rectRounded',
          }));
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          const description = [
            'Tempo médio de atendimento',
            'Tempo médio de fila',
            'Tempo médio de respostas',
            'Tempo médio de espera',
          ].includes(tooltipItem.dataset.label)
            ? tooltipItem.raw > 1
              ? 'minutos'
              : 'minuto'
            : tooltipItem.dataset.label.toLowerCase();
          return `${formatNumberForChart(tooltipItem.raw)} ${description}`;
        },
      },
    },
  },
  scales: {
    y: {
      type: 'linear',
      position: 'left',
      grid: {
        drawOnChartArea: true,
        color: 'rgba(0,0,0,.20)',
      },
      ticks: {
        stepSize: 20,
      },
    },
    y1: {
      type: 'linear',
      position: 'right',
      ticks: {
        callback: (value: any) => `${value}m`,
      },
      grid: {
        drawOnChartArea: false,
        color: 'rgba(0,0,0,.16)',
      },
    },
  },
};
