import { BdsAutocompleteCustomEvent } from 'blip-ds';
import { BdsAutocomplete } from 'blip-ds/dist/blip-ds-react/components';
import { AutocompleteMultiSelectedChangeEventDetail } from 'blip-ds/dist/types/components/autocomplete/autocomplete-select-interface';
import React from 'react';
import './filter_select.scss';

interface FilterSelectProps {
  label: string;
  placeholder: string;
  selectedAll?: boolean;
  selectionType?: 'single' | 'multiple';
  options: FilterSelectOption[];
  value: any;
  onChange: (event: BdsAutocompleteCustomEvent<AutocompleteMultiSelectedChangeEventDetail>) => void;
}

export interface FilterSelectOption {
  label: string;
  value: string;
}

const FilterSelect = ({
  label,
  placeholder,
  options,
  value,
  selectionType = 'multiple',
  selectedAll = false,
  onChange,
}: FilterSelectProps) => {
  return (
    <div className="filter-select">
      <BdsAutocomplete
        selectionType={selectionType}
        selectedAll={selectedAll}
        label={label}
        placeholder={placeholder}
        options={options}
        onBdsMultiselectedChange={onChange}
        value={value}
        clearIconOnFocus
      />
    </div>
  );
};

export default FilterSelect;
