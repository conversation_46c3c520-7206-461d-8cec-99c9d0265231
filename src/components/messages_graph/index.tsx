import React from 'react';
import { translation } from './i18n/translation';
import { useTranslation } from '@hooks/useTranslation';
import { getDatasets, options } from './graph_settings';
import './messages_graph.scss';
import LineBarChart from '@components/line_bar_chart';
export interface MessagesGraphData {
  labelPeriods: string[];
  sentMessages: number[];
  receivedMessages: number[];
  activeMessages: number[];
  perUserMessage: number[];
}


const MessagesGraph = (data: MessagesGraphData) => {
  const { translate } = useTranslation();
  const translations = translate(translation);
  const datasets = getDatasets(data);

  return (
    <LineBarChart
      title={translations.blip_insights_messages_graph.title}
      description={translations.blip_insights_messages_graph.subtitle}
      labelPeriods={data.labelPeriods}
      datasets={datasets}
      chartOptions={options}
    />
  );
};

export default MessagesGraph;
