import { MessagesGraphData } from '@typings/OverviewFilter';
import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';
import { Chart } from 'chart.js';

export const getDatasets = ({ sentMessages, receivedMessages, activeMessages, perUserMessage }: MessagesGraphData) => [
  {
    label: 'Mensagens enviadas',
    type: 'bar' as const,
    backgroundColor: 'rgba(25, 104, 240, 0.65)',
    data: sentMessages,
    borderColor: 'rgba(25, 104, 240, 0.65)',
    borderWidth: 2,
    borderRadius: 10,
    minBarLength: 22,
    order: 1,
  },
  {
    label: 'Mensagens recebidas',
    type: 'bar' as const,
    backgroundColor: 'rgba(253, 155, 220, 0.65)',
    data: receivedMessages,
    borderColor: 'rgba(253, 155, 220, 1)',
    borderWidth: 2,
    borderRadius: 10,
    minBarLength: 22,
    order: 1,
  },
  {
    label: 'Mensagens ativas',
    type: 'bar' as const,
    borderColor: 'rgba(132, 235, 188, 1)',
    backgroundColor: 'rgba(132, 235, 188, 1)',
    data: activeMessages,
    borderWidth: 2,
    borderRadius: 10,
    minBarLength: 22,
    order: 1,
  },
  {
    label: 'Mensagens por usuário',
    type: 'line' as const,
    borderColor: 'rgba(240, 99, 5, 1)',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 99, 5, 1)',
    data: perUserMessage,
    yAxisID: 'y1',
  },
];

export const options: any = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        generateLabels: (chart: Chart) => {
          return chart.data.datasets.map((dataset) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            lineWidth: dataset.type === 'line' ? 2 : 0,
            strokeStyle: dataset.borderColor || 'black',
            pointStyle: dataset.type === 'line' ? 'dash' : 'rectRounded',
          }));
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(69, 69, 69, 1)',
      bodyColor: 'rgba(246, 246, 246, 1)',
      borderColor: '#fff',
      borderWidth: 0,
      padding: 10,
      displayColors: false,
      callbacks: {
        title: () => '',
        label: (tooltipItem: any) => {
          const labels = ['Mensagens por usuário'];
          if (labels.includes(tooltipItem.dataset.label)) {
            return `${tooltipItem.raw} de ${tooltipItem.dataset.label.toLowerCase()}`;
          }
          return `${formatNumberForChart(tooltipItem.raw)} ${tooltipItem.dataset.label.toLowerCase()}`;
        },
      },
    },
  },
  scales: {
    y: {
      type: 'linear',
      position: 'left',
      grid: {
        drawOnChartArea: true,
        color: 'rgba(0,0,0,.20)',
      },
      ticks: {
        padding: 5,
        maxTicksLimit: 800,
      },
    },
    y1: {
      type: 'linear',
      position: 'right',
      ticks: {
        min: 0,
        maxTicksLimit: 400,
        padding: 10,
      },
      grid: {
        drawOnChartArea: false,
        color: 'rgba(0,0,0,.16)',
      },
    },
  },
};
