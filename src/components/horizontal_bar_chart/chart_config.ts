import { Chart, ChartOptions, Plugin } from 'chart.js';
import { HorizontalBarDataset } from '.';
import { formatNumberForChart } from '@utils/helpdesk/PerformanceGraphHelper';

export const parseDatasets = (datasets: {label: string, value: number, percentage: number}[]): [string[], number[]] => {
  datasets.sort((a, b) => b.value - a.value);
  const labels: string[] = [];
  const dataset: number[] = [];

  datasets.forEach((item) => {
    labels.push(item.label);
    dataset.push(item.value);
  })

  return [labels, dataset]
}

export const getOptions = () : ChartOptions<'bar'> => {
  return {
    indexAxis: 'y' as const,
    maintainAspectRatio: false,
    responsive: true,
    parsing: {
      yAxisKey: 'label',
      xAxisKey: 'value'
    },
    layout: {
      padding: {
        right: 52
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          crossAlign: 'far',
          color: 'rgba(69, 69, 69, 1)',
          font: {
            family: "'Nunito Sans', sans-serif", 
            size: 16,  
            weight: 'bold',
          },
        },
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
      },
      x: {
        beginAtZero: true,
        grid: {
          display: false,
        },
        ticks: {
          display: false,
        },
        border: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(69, 69, 69, 1)',
        bodyColor: 'rgba(246, 246, 246, 1)',
        borderColor: '#fff',
        borderWidth: 0,
        padding: 10,
        displayColors: false,
        callbacks: {
          title: () => '',
          label: (tooltipItem) => {
            const dataset = tooltipItem.raw as HorizontalBarDataset;
            return `${formatNumberForChart(dataset.value)} respostas`;
          },
        },
      }
    },
  }
}

export const drawBarLabels: Plugin<'bar'> = {
  id: 'barValueLabels',
  afterDatasetsDraw: (chart: Chart<'bar', any, string>) => {
    const { ctx } = chart;

    chart.data.datasets.forEach((dataset, datasetIndex) => {
      const meta = chart.getDatasetMeta(datasetIndex);

      meta.data.forEach((bar, index) => {
        const value = dataset.data[index];
        if (value === null) return;
        const position = bar.tooltipPosition(true);
        ctx.save();
        ctx.fillStyle = 'rgba(69, 69, 69, 1)';
        ctx.font = '700 16px "Nunito Sans", sans-serif';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${value.percentage}%`, position.x + 8, position.y);
        ctx.restore();
      });
    });
  },
};
