import React from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, Tooltip, Legend, BarElement } from 'chart.js';
import { Chart as ReactChart } from 'react-chartjs-2';
import './horizontal_bar_chart.scss';
import { drawBarLabels, getOptions, parseDatasets } from './chart_config';

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend);

ChartJS.defaults.font = {
  family: "'Nunito Sans', sans-serif",
  size: 14,
  weight: 'normal',
  style: 'normal',
};

export interface HorizontalBarDataset {
  label: string;
  value: number;
  percentage: number;
}

interface HorizontalBarChartBaseProps {
  title: string;
  description: string;
  datasets: HorizontalBarDataset[];
  chartColor?: string;
}

const rowHeight = 50;

const HorizontalBarChart: React.FC<HorizontalBarChartBaseProps> = ({
  title,
  description,
  datasets,
  chartColor = 'rgba(25, 104, 240, 1)',
}) => {
  const labels = datasets.map((ds) => ds.label);
  const options = getOptions();

  const data = {
    labels,
    datasets: [
      {
        data: datasets,
        backgroundColor: chartColor,
        borderRadius: 8,
        barThickness: 30,
      },
    ],
  };

  return (
    <div className="horizontal_bar_chart">
      <div className="horizontal_bar_chart__header">
        <span>
          <h4 className="horizontal_bar_chart__title">{title}</h4>
          <p className="horizontal_bar_chart__description">{description}</p>
        </span>
      </div>
      {datasets && datasets.length > 0 ? (
        <div className="horizontal_bar_chart__container" style={{ height: rowHeight * labels.length }}>
          <ReactChart type="bar" data={data} options={options} plugins={[drawBarLabels]} />
        </div>
      ) : (
        <div className="proactive-messages__no-data-message">
          <bds-typo variant="fs-16" bold="bold">
            Não há dados para o filtro selecionado
          </bds-typo>
        </div>
      )}
    </div>
  );
};

export default HorizontalBarChart;
