import * as React from 'react';
import htmlToReact from 'html-to-react';
import { createRoot } from 'react-dom/client';

import { App } from './App.tsx';
import { constants } from './constants';

// This class is required to create the web component
// Based (probably) on https://blog.piotrnalepa.pl/2020/03/27/converting-react-components-to-web-components/
class BlipInsightsElement extends HTMLElement {
  constructor() {
    super();
    this.observer = new MutationObserver(() => this.update());
    this.observer.observe(this, { attributes: true });
    this.customInfo = [];
    this.root = createRoot(this);
  }

  sendInfo(data) {
    this.customInfo = data;
    this.update();
  }

  connectedCallback() {
    this._innerHTML = this.innerHTML;
    this.mount();
  }

  disconnectedCallback() {
    this.unmount();
    this.observer.disconnect();
  }

  update() {
    this.mount();
  }

  mount() {
    const propTypes = App.propTypes ? App.propTypes : {};
    const events = App.propTypes ? App.propTypes : {};
    let props = {
      ...this.getProps(this.attributes, propTypes),
      ...this.getEvents(events),
      children: this.parseHtmlToReact(this.innerHTML),
      ...this.customInfo,
    };
    this.root.render(<App {...props} />);
  }

  unmount() {
    this.root.unmount();
  }

  parseHtmlToReact(html) {
    return html && new htmlToReact.Parser().parse(html);
  }

  getProps(attributes, propTypes) {
    propTypes = propTypes || {};
    return [...attributes]
      .filter((attr) => attr.name !== 'style')
      .map((attr) => this.convert(propTypes, attr.name, attr.value))
      .reduce((props, prop) => ({ ...props, [prop.name]: prop.value }), {});
  }

  getEvents(propTypes) {
    return Object.keys(propTypes)
      .filter((key) => /on([A-Z].*)/.exec(key))
      .reduce(
        (events, ev) => ({
          ...events,
          [ev]: (args) => this.dispatchEvent(new CustomEvent(ev, { ...args })),
        }),
        {}
      );
  }

  convert(propTypes, attrName, attrValue) {
    const propName = Object.keys(propTypes).find((key) => key.toLowerCase() == attrName);
    let value = attrValue;
    try {
      if (attrValue === 'true' || attrValue === 'false') value = attrValue == 'true';
      else if (!isNaN(attrValue) && attrValue !== '') value = +attrValue;
      else if (/^{.*}/.exec(attrValue)) value = JSON.parse(attrValue);
    } catch (e) {
      console.warn('Error converting MFE attribute', e);
    }
    return {
      name: propName ? propName : attrName,
      value: value,
    };
  }
}

customElements.define(constants.applicationName, BlipInsightsElement);
