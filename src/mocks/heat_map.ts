import { BackendResponse } from '@services/BlipInsightsService';

export default {
  interval: 8279,
  buttonOptions: ['<PERSON><PERSON><PERSON><PERSON> de abert<PERSON>', '<PERSON><PERSON><PERSON><PERSON> de fechamento'],
  parsedData: [
    [
      2452, 2029, 1580, 1076, 671, 1395, 1152, 901, 1244, 2050, 3293, 3241,
      3077, 3592, 2919, 3054, 3975, 4437, 3700, 3330, 3713, 6733, 8620, 2740,
    ],
    [
      3047, 1912, 1116, 793, 442, 353, 546, 923, 1719, 2373, 2766, 2887, 3195,
      2984, 2802, 3791, 3318, 3410, 3298, 3666, 3754, 3308, 3160, 2510,
    ],
    [
      2035, 1661, 1340, 1298, 700, 761, 733, 1771, 2128, 2562, 3223, 3272, 3962,
      3976, 3827, 4108, 4289, 3552, 3589, 4174, 4031, 2756, 2597, 1980,
    ],
    [
      2472, 1665, 1132, 821, 935, 715, 591, 1061, 1476, 1968, 2399, 2594, 3962,
      5949, 3862, 3512, 3225, 3474, 3693, 3679, 4307, 3019, 3141, 2291,
    ],
    [
      2375, 1568, 1146, 692, 616, 499, 682, 1034, 1743, 2247, 2250, 2648, 2892,
      3215, 2775, 3156, 2821, 2915, 2849, 3106, 2984, 2660, 2533, 2445,
    ],
    [
      1989, 1741, 1062, 703, 341, 587, 606, 1013, 1487, 1866, 2100, 2532, 2420,
      2571, 2897, 2298, 2475, 3242, 3251, 3137, 3581, 2792, 3355, 2351,
    ],
    [
      2061, 1827, 1323, 1364, 1334, 796, 679, 1190, 1677, 3070, 2831, 3756,
      3912, 3965, 3621, 4446, 3540, 3837, 3988, 3868, 3464, 2767, 2270, 2324,
    ],
  ],
  processApiResponse: (response: BackendResponse) => {
    const sampleData = response.result.data_array.filter(
      (weekArray: string[]) => weekArray[0] !== null,
    );
    const parsedSampleData = sampleData.map((weekday: string[]) => {
      weekday.shift(); // primeiro valor é o nome do dia ex.: domingo
      return weekday.map(valueHour => parseFloat(valueHour));
    });
    const min = Math.min(
      ...parsedSampleData.map((weekday: number[]) => Math.min(...weekday)),
    );
    const max = Math.max(
      ...parsedSampleData.map((weekday: number[]) => Math.max(...weekday)),
    );
    return {
      data: parsedSampleData,
      interval: max - min,
    };
  },
  apiResponse: {
    statement_id: '01f016d3-ae21-10fb-9ed7-2732635d3a20',
    status: {
      state: 'SUCCEEDED',
    },
    manifest: {
      format: 'JSON_ARRAY',
      schema: {
        column_count: 25,
        columns: [
          {
            name: 'dia_semanafechamento',
            type_text: 'STRING',
            type_name: 'STRING',
            position: 0,
          },
          {
            name: '00h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 1,
          },
          {
            name: '01h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 2,
          },
          {
            name: '02h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 3,
          },
          {
            name: '03h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 4,
          },
          {
            name: '04h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 5,
          },
          {
            name: '05h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 6,
          },
          {
            name: '06h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 7,
          },
          {
            name: '07h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 8,
          },
          {
            name: '08h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 9,
          },
          {
            name: '09h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 10,
          },
          {
            name: '10h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 11,
          },
          {
            name: '11h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 12,
          },
          {
            name: '12h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 13,
          },
          {
            name: '13h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 14,
          },
          {
            name: '14h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 15,
          },
          {
            name: '15h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 16,
          },
          {
            name: '16h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 17,
          },
          {
            name: '17h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 18,
          },
          {
            name: '18h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 19,
          },
          {
            name: '19h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 20,
          },
          {
            name: '20h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 21,
          },
          {
            name: '21h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 22,
          },
          {
            name: '22h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 23,
          },
          {
            name: '23h',
            type_text: 'BIGINT',
            type_name: 'LONG',
            position: 24,
          },
        ],
      },
      total_chunk_count: 1,
      chunks: [
        {
          chunk_index: 0,
          row_offset: 0,
          row_count: 8,
        },
      ],
      total_row_count: 8,
      truncated: false,
    },
    result: {
      chunk_index: 0,
      row_offset: 0,
      row_count: 8,
      data_array: [
        [
          null,
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
          '0',
        ],
        [
          'domingo',
          '3313',
          '2599',
          '2092',
          '1418',
          '1535',
          '1733',
          '1546',
          '1210',
          '1342',
          '2339',
          '3555',
          '3404',
          '3544',
          '3611',
          '2948',
          '3088',
          '3987',
          '4446',
          '3724',
          '3343',
          '3752',
          '6759',
          '8736',
          '2740',
        ],
        [
          'segunda',
          '3198',
          '1920',
          '1123',
          '794',
          '442',
          '353',
          '546',
          '946',
          '1735',
          '2411',
          '2804',
          '2931',
          '3229',
          '3013',
          '2839',
          '3835',
          '3350',
          '3458',
          '3316',
          '3700',
          '3785',
          '3353',
          '3196',
          '2546',
        ],
        [
          'terça',
          '2972',
          '2752',
          '2008',
          '1517',
          '758',
          '765',
          '735',
          '1807',
          '2154',
          '2593',
          '3264',
          '3319',
          '4008',
          '4009',
          '3849',
          '4122',
          '4301',
          '3589',
          '3617',
          '4205',
          '4066',
          '2779',
          '2615',
          '1996',
        ],
        [
          'quarta',
          '2483',
          '1749',
          '1168',
          '846',
          '937',
          '722',
          '592',
          '1068',
          '1635',
          '1973',
          '2426',
          '2620',
          '4007',
          '5997',
          '3880',
          '3558',
          '3261',
          '3516',
          '3754',
          '3719',
          '4338',
          '3048',
          '3173',
          '2303',
        ],
        [
          'quinta',
          '2552',
          '1772',
          '1179',
          '695',
          '616',
          '505',
          '736',
          '1039',
          '1786',
          '2278',
          '2357',
          '2701',
          '2938',
          '3251',
          '2803',
          '3190',
          '2924',
          '3018',
          '2944',
          '3156',
          '3026',
          '2681',
          '2549',
          '2469',
        ],
        [
          'sexta',
          '2259',
          '1943',
          '1082',
          '708',
          '471',
          '589',
          '621',
          '1036',
          '1497',
          '1913',
          '2141',
          '2569',
          '2464',
          '2592',
          '2938',
          '2327',
          '2510',
          '3268',
          '3282',
          '3156',
          '3623',
          '2828',
          '3373',
          '2376',
        ],
        [
          'sábado',
          '2974',
          '2646',
          '2371',
          '1491',
          '1366',
          '801',
          '695',
          '1190',
          '1684',
          '3079',
          '2841',
          '3767',
          '3937',
          '3981',
          '3637',
          '4459',
          '3564',
          '3870',
          '4009',
          '3908',
          '3477',
          '2782',
          '2279',
          '2326',
        ],
      ],
    },
  },
};
